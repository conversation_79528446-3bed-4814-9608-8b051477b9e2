{".class": "MypyFile", "_fullname": "limits", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "RateLimitItem": {".class": "SymbolTableNode", "cross_ref": "limits.limits.RateLimitItem", "kind": "Gdef"}, "RateLimitItemPerDay": {".class": "SymbolTableNode", "cross_ref": "limits.limits.RateLimitItemPerDay", "kind": "Gdef"}, "RateLimitItemPerHour": {".class": "SymbolTableNode", "cross_ref": "limits.limits.RateLimitItemPerHour", "kind": "Gdef"}, "RateLimitItemPerMinute": {".class": "SymbolTableNode", "cross_ref": "limits.limits.RateLimitItemPerMinute", "kind": "Gdef"}, "RateLimitItemPerMonth": {".class": "SymbolTableNode", "cross_ref": "limits.limits.RateLimitItemPerMonth", "kind": "Gdef"}, "RateLimitItemPerSecond": {".class": "SymbolTableNode", "cross_ref": "limits.limits.RateLimitItemPerSecond", "kind": "Gdef"}, "RateLimitItemPerYear": {".class": "SymbolTableNode", "cross_ref": "limits.limits.RateLimitItemPerYear", "kind": "Gdef"}, "WindowStats": {".class": "SymbolTableNode", "cross_ref": "limits.util.WindowStats", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "limits.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "limits.__version__", "name": "__version__", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}}}, "_version": {".class": "SymbolTableNode", "cross_ref": "limits._version", "kind": "Gdef", "module_public": false}, "aio": {".class": "SymbolTableNode", "cross_ref": "limits.aio", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "parse": {".class": "SymbolTableNode", "cross_ref": "limits.util.parse", "kind": "Gdef"}, "parse_many": {".class": "SymbolTableNode", "cross_ref": "limits.util.parse_many", "kind": "Gdef"}, "storage": {".class": "SymbolTableNode", "cross_ref": "limits.storage", "kind": "Gdef"}, "strategies": {".class": "SymbolTableNode", "cross_ref": "limits.strategies", "kind": "Gdef"}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\__init__.py"}
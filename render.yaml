services:
  # Web service
  - type: web
    name: biocleaning
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn app.app:app --host 0.0.0.0 --port $PORT
    envVars:
      - key: ENVIRONMENT
        value: production
      - key: SECRET
        sync: false
      - key: BASE_URL
        sync: false
      - key: MAIL_USERNAME
        sync: false
      - key: MAIL_PASSWORD
        sync: false
      - key: MAIL_FROM
        sync: false
      - key: MAIL_PORT
        sync: false
      - key: MAIL_SERVER
        sync: false
      - key: MAIL_FROM_NAME
        sync: false
      - key: MAIL_STARTTLS
        sync: false
      - key: MAIL_SSL_TLS
        sync: false
      - key: MAIL_USE_CREDENTIALS
        sync: false
      - key: <PERSON><PERSON>_VALIDATE_CERTS
        sync: false

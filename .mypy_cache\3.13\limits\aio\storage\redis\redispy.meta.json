{"data_mtime": 1752571432, "dep_lines": [6, 7, 8, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 11, 11], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 25, 25], "dependencies": ["limits.aio.storage.redis.bridge", "limits.errors", "limits.typing", "__future__", "time", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "types", "urllib", "urllib.parse"], "hash": "cad2a51a71e13c6d7cdf2812abb3ad8ef5c3f84b", "id": "limits.aio.storage.redis.redispy", "ignore_all": true, "interface_hash": "20e30a23774e7f48cfcc5681b57f49426fe4b818", "mtime": 1752570205, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\aio\\storage\\redis\\redispy.py", "plugin_data": null, "size": 8310, "suppressed": ["redis.commands", "redis"], "version_id": "1.15.0"}
{"data_mtime": 1752571431, "dep_lines": [9, 7, 1, 3, 4, 5, 1, 1, 1, 1, 12], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 25], "dependencies": ["limits.aio.storage.memcached.bridge", "limits.typing", "__future__", "time", "math", "types", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "234cc3129a171ba3af1469c7045bcbe2084180b9", "id": "limits.aio.storage.memcached.emcache", "ignore_all": true, "interface_hash": "66f483733b83fdda5c9882a1dc087b067d6f2cd6", "mtime": 1752570205, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\aio\\storage\\memcached\\emcache.py", "plugin_data": null, "size": 3833, "suppressed": ["emcache"], "version_id": "1.15.0"}
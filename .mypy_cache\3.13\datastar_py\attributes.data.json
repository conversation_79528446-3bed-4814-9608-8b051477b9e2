{".class": "MypyFile", "_fullname": "datastar_py.attributes", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AttrGroup": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.AttrGroup", "name": "AttrGroup", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttrGroup", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.AttrGroup", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttrGroup.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["datastar_py.attributes.AttrGroup", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of AttrGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__html__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.attributes.AttrGroup.__html__", "name": "__html__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datastar_py.attributes.AttrGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttrGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["datastar_py.attributes.AttrGroup", {".class": "Instance", "args": ["datastar_py.attributes.BaseAttr"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AttrGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttrGroup.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datastar_py.attributes.AttrGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of AttrGroup", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttrGroup.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datastar_py.attributes.AttrGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of AttrGroup", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttrGroup.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datastar_py.attributes.AttrGroup"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of AttrGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_attr_dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttrGroup._attr_dict", "name": "_attr_dict", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_attr_string": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttrGroup._attr_string", "name": "_attr_string", "type": "builtins.str"}}, "_attrs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttrGroup._attrs", "name": "_attrs", "type": {".class": "Instance", "args": ["datastar_py.attributes.BaseAttr"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.AttrGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.AttrGroup", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AttributeGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.AttributeGenerator", "name": "AttributeGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.AttributeGenerator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "alias"], "arg_types": ["datastar_py.attributes.AttributeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AttributeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator._alias", "name": "_alias", "type": "builtins.str"}}, "attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": [null, null, "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.attr", "name": "attr", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": [null, null, "attrs"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attr of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "signal_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.bind", "name": "bind", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "signal_name"], "arg_types": ["datastar_py.attributes.AttributeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bind of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "class_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": [null, null, "classes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.class_", "name": "class_", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": [null, null, "classes"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "class_ of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "computed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": [null, null, "computed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.computed", "name": "computed", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": [null, null, "computed"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "computed of AttributeGenerator", "ret_type": "datastar_py.attributes.AttrGroup", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "custom_validity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.custom_validity", "name": "custom_validity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "custom_validity of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ignore_morph": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.AttributeGenerator.ignore_morph", "name": "ignore_morph", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.AttributeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore_morph of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator.ignore_morph", "name": "ignore_morph", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.AttributeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ignore_morph of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "indicator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "signal_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.indicator", "name": "indicator", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "signal_name"], "arg_types": ["datastar_py.attributes.AttributeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "indicator of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json_signals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.AttributeGenerator.json_signals", "name": "json_signals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.AttributeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "json_signals of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator.json_signals", "name": "json_signals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.AttributeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "json_signals of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "on": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.on", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": {".class": "UnionType", "items": ["datastar_py.attributes.OnAttr", "datastar_py.attributes.OnIntervalAttr", "datastar_py.attributes.OnLoadAttr", "datastar_py.attributes.OnRafAttr", "datastar_py.attributes.OnSignalChangeAttr"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "interval"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnIntervalAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "interval"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnIntervalAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "load"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnLoadAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "load"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnLoadAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "raf"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnRafAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "raf"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnRafAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "signal-change"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnSignalChangeAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "signal-change"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnSignalChangeAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "datastar_py.attributes.JSEvent"}, "builtins.str"], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator.on", "name": "on", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "datastar_py.attributes.JSEvent"}, "builtins.str"], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "interval"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnIntervalAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "load"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnLoadAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "raf"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnRafAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "LiteralType", "fallback": "builtins.str", "value": "signal-change"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnSignalChangeAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "event", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "datastar_py.attributes.JSEvent"}, "builtins.str"], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on of AttributeGenerator", "ret_type": "datastar_py.attributes.OnAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "persist": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.AttributeGenerator.persist", "name": "persist", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.AttributeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persist of AttributeGenerator", "ret_type": "datastar_py.attributes.PersistAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator.persist", "name": "persist", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.AttributeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "persist of AttributeGenerator", "ret_type": "datastar_py.attributes.PersistAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "preserve_attr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.preserve_attr", "name": "preserve_attr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "attrs"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "preserve_attr of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "signal_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.ref", "name": "ref", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "signal_name"], "arg_types": ["datastar_py.attributes.AttributeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ref of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "replace_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url_expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.replace_url", "name": "replace_url", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url_expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "replace_url of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "scroll_into_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.AttributeGenerator.scroll_into_view", "name": "scroll_into_view", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.AttributeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scroll_into_view of AttributeGenerator", "ret_type": "datastar_py.attributes.ScrollIntoViewAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator.scroll_into_view", "name": "scroll_into_view", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.AttributeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "scroll_into_view of AttributeGenerator", "ret_type": "datastar_py.attributes.ScrollIntoViewAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "show": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.show", "name": "show", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "show of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 4], "arg_names": [null, null, "expressions_", "signals"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.signals", "name": "signals", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 4], "arg_names": [null, null, "expressions_", "signals"], "arg_types": ["datastar_py.attributes.AttributeGenerator", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "datastar_py.attributes.SignalValue"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "datastar_py.attributes.SignalValue"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signals of AttributeGenerator", "ret_type": "datastar_py.attributes.SignalsAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "star_ignore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.AttributeGenerator.star_ignore", "name": "star_ignore", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.AttributeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "star_ignore of AttributeGenerator", "ret_type": "datastar_py.attributes.StarIgnoreAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.AttributeGenerator.star_ignore", "name": "star_ignore", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.AttributeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "star_ignore of AttributeGenerator", "ret_type": "datastar_py.attributes.StarIgnoreAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.text", "name": "text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "text of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "view_transition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expression"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.AttributeGenerator.view_transition", "name": "view_transition", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expression"], "arg_types": ["datastar_py.attributes.AttributeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "view_transition of AttributeGenerator", "ret_type": "datastar_py.attributes.BaseAttr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.AttributeGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.AttributeGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "BaseAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.BaseAttr", "name": "BaseAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.BaseAttr", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.BaseAttr", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.BaseAttr.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.BaseAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of BaseAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.BaseAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.BaseAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}]}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.BaseAttr.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["datastar_py.attributes.BaseAttr", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of BaseAttr", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__html__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.attributes.BaseAttr.__html__", "name": "__html__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datastar_py.attributes.BaseAttr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "attr", "value", "suffix", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.BaseAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 5], "arg_names": ["self", "attr", "value", "suffix", "alias"], "arg_types": ["datastar_py.attributes.BaseAttr", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BaseAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.BaseAttr.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datastar_py.attributes.BaseAttr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of BaseAttr", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.BaseAttr.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datastar_py.attributes.BaseAttr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of BaseAttr", "ret_type": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.BaseAttr.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["datastar_py.attributes.BaseAttr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of BaseAttr", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_alias": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.BaseAttr._alias", "name": "_alias", "type": "builtins.str"}}, "_attr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.BaseAttr._attr", "name": "_attr", "type": "builtins.str"}}, "_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.BaseAttr._key", "name": "_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["datastar_py.attributes.BaseAttr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_key of BaseAttr", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mods": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.BaseAttr._mods", "name": "_mods", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_suffix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.BaseAttr._suffix", "name": "_suffix", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_to_kebab_suffix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "signal_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.BaseAttr._to_kebab_suffix", "name": "_to_kebab_suffix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "signal_name"], "arg_types": ["datastar_py.attributes.BaseAttr", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_to_kebab_suffix of BaseAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.BaseAttr._value", "name": "_value", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.BaseAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_public": false}, "JSEvent": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "datastar_py.attributes.JSEvent", "line": 10, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "abort"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "afterprint"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "animationend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "animationiteration"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "animationstart"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "beforeprint"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "beforeunload"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "blur"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "canplay"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "canplaythrough"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "change"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "click"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "contextmenu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "copy"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cut"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dblclick"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "drag"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dragend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dragenter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dragleave"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dragover"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "dragstart"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "drop"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "durationchange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ended"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "focus"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "focusin"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "focusout"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fullscreenchange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fullscreenerror"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "hashchange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "input"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "invalid"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keydown"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keypress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "keyup"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "load"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "loadeddata"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "loadedmetadata"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "loadstart"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "message"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mousedown"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mouseenter"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mouseleave"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mousemove"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mouseover"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mouseout"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mouseup"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "mousewheel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "offline"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "online"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "open"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pagehide"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pageshow"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "paste"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "pause"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "play"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "playing"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "popstate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ratechange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "resize"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "reset"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scroll"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "search"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "seeked"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "seeking"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "select"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "show"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "stalled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "storage"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "submit"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "suspend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "timeupdate"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "toggle"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "touchcancel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "touchend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "touchmove"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "touchstart"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "transitionend"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "unload"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "volumechange"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "waiting"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "wheel"}], "uses_pep604_syntax": false}}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "OnAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.attributes.BaseAttr", "datastar_py.attributes.TimingMod", "datastar_py.attributes.ViewtransitionMod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.OnAttr", "name": "OnAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnAttr", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.OnAttr", "datastar_py.attributes.BaseAttr", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "datastar_py.attributes.TimingMod", "datastar_py.attributes.ViewtransitionMod", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "event", "expression", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5], "arg_names": ["self", "event", "expression", "alias"], "arg_types": ["datastar_py.attributes.OnAttr", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OnAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "capture": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.OnAttr.capture", "name": "capture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capture of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.OnAttr.capture", "name": "capture", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "capture of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}}}, "once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.OnAttr.once", "name": "once", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "once of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.OnAttr.once", "name": "once", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "once of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}}}, "outside": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.OnAttr.outside", "name": "outside", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outside of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.OnAttr.outside", "name": "outside", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outside of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}}}, "passive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.OnAttr.passive", "name": "passive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "passive of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.OnAttr.passive", "name": "passive", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "passive of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}}}, "prevent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.OnAttr.prevent", "name": "prevent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prevent of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.OnAttr.prevent", "name": "prevent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prevent of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}}}, "stop": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.OnAttr.stop", "name": "stop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.OnAttr.stop", "name": "stop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stop of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}}}, "trust": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.OnAttr.trust", "name": "trust", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trust of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.OnAttr.trust", "name": "trust", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "trust of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}}}, "window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.OnAttr.window", "name": "window", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "window of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.OnAttr.window", "name": "window", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "window of OnAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OnIntervalAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.attributes.BaseAttr", "datastar_py.attributes.ViewtransitionMod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.OnIntervalAttr", "name": "OnIntervalAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnIntervalAttr", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.OnIntervalAttr", "datastar_py.attributes.BaseAttr", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "datastar_py.attributes.ViewtransitionMod", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "expression", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnIntervalAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "expression", "alias"], "arg_types": ["datastar_py.attributes.OnIntervalAttr", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OnIntervalAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "duration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "duration", "leading"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnIntervalAttr.duration", "name": "duration", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "duration", "leading"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnIntervalAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnIntervalAttr", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.str"], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "duration of OnIntervalAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnIntervalAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnIntervalAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnIntervalAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnIntervalAttr", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnIntervalAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnIntervalAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OnLoadAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.attributes.BaseAttr", "datastar_py.attributes.ViewtransitionMod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.OnLoadAttr", "name": "OnLoadAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnLoadAttr", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.OnLoadAttr", "datastar_py.attributes.BaseAttr", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "datastar_py.attributes.ViewtransitionMod", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "expression", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnLoadAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "expression", "alias"], "arg_types": ["datastar_py.attributes.OnLoadAttr", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OnLoadAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delay": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "delay"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnLoadAttr.delay", "name": "delay", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "delay"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnLoadAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnLoadAttr", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.str"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delay of OnLoadAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnLoadAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnLoadAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnLoadAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnLoadAttr", "values": [], "variance": 0}]}}}, "once": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.OnLoadAttr.once", "name": "once", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnLoadAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnLoadAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "once of OnLoadAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnLoadAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnLoadAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnLoadAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnLoadAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.OnLoadAttr.once", "name": "once", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnLoadAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnLoadAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "once of OnLoadAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnLoadAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnLoadAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnLoadAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnLoadAttr", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnLoadAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnLoadAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OnRafAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.attributes.BaseAttr", "datastar_py.attributes.TimingMod", "datastar_py.attributes.ViewtransitionMod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.OnRafAttr", "name": "OnRafAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnRafAttr", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.OnRafAttr", "datastar_py.attributes.BaseAttr", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "datastar_py.attributes.TimingMod", "datastar_py.attributes.ViewtransitionMod", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "expression", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnRafAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "expression", "alias"], "arg_types": ["datastar_py.attributes.OnRafAttr", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OnRafAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnRafAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnRafAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OnSignalChangeAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.attributes.BaseAttr", "datastar_py.attributes.TimingMod", "datastar_py.attributes.ViewtransitionMod"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.OnSignalChangeAttr", "name": "OnSignalChangeAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnSignalChangeAttr", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.OnSignalChangeAttr", "datastar_py.attributes.BaseAttr", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "datastar_py.attributes.TimingMod", "datastar_py.attributes.ViewtransitionMod", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "expression", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.OnSignalChangeAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "expression", "alias"], "arg_types": ["datastar_py.attributes.OnSignalChangeAttr", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of OnSignalChangeAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.OnSignalChangeAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.OnSignalChangeAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PersistAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.attributes.BaseAttr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.PersistAttr", "name": "PersistAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.PersistAttr", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.PersistAttr", "datastar_py.attributes.BaseAttr", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "signal_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.PersistAttr.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "signal_names"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.PersistAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.PersistAttr", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of PersistAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.PersistAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.PersistAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.PersistAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.PersistAttr", "values": [], "variance": 0}]}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.PersistAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "alias"], "arg_types": ["datastar_py.attributes.PersistAttr", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PersistAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "session": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.PersistAttr.session", "name": "session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.PersistAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.PersistAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session of PersistAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.PersistAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.PersistAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.PersistAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.PersistAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.PersistAttr.session", "name": "session", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.PersistAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.PersistAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "session of PersistAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.PersistAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.PersistAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.PersistAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.PersistAttr", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.PersistAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.PersistAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ScrollIntoViewAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.attributes.BaseAttr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.ScrollIntoViewAttr", "name": "ScrollIntoViewAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.ScrollIntoViewAttr", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.ScrollIntoViewAttr", "datastar_py.attributes.BaseAttr", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "alias"], "arg_types": ["datastar_py.attributes.ScrollIntoViewAttr", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ScrollIntoViewAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "auto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.auto", "name": "auto", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auto of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.auto", "name": "auto", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "auto of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "focus": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.focus", "name": "focus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "focus of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.focus", "name": "focus", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "focus of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "hcenter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.hcenter", "name": "hcenter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hcenter of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.hcenter", "name": "hcenter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hcenter of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "hend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.hend", "name": "hend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hend of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.hend", "name": "hend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hend of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "hnearest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.hnearest", "name": "hnearest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hnearest of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.hnearest", "name": "hnearest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hnearest of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "hstart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.hstart", "name": "hstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hstart of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.hstart", "name": "hstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hstart of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "instant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.instant", "name": "instant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instant of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.instant", "name": "instant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "instant of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "smooth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.smooth", "name": "smooth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "smooth of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.smooth", "name": "smooth", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "smooth of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "vcenter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.vcenter", "name": "vcenter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vcenter of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.vcenter", "name": "vcenter", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vcenter of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "vend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.vend", "name": "vend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vend of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.vend", "name": "vend", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vend of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "vnearest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.vnearest", "name": "vnearest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vnearest of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.vnearest", "name": "vnearest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vnearest of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}, "vstart": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.vstart", "name": "vstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vstart of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ScrollIntoViewAttr.vstart", "name": "vstart", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "vstart of ScrollIntoViewAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ScrollIntoViewAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ScrollIntoViewAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing.Self", "kind": "Gdef", "module_public": false}, "SignalValue": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "datastar_py.attributes.SignalValue", "line": 99, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.int", "builtins.float", "builtins.bool", {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "datastar_py.attributes.SignalValue"}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "datastar_py.attributes.SignalValue"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "SignalsAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.attributes.BaseAttr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.SignalsAttr", "name": "SignalsAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.SignalsAttr", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.SignalsAttr", "datastar_py.attributes.BaseAttr", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "signals_object", "expressions", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.SignalsAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "signals_object", "expressions", "alias"], "arg_types": ["datastar_py.attributes.SignalsAttr", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.bool", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SignalsAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ifmissing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.SignalsAttr.ifmissing", "name": "ifmissing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.SignalsAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.SignalsAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ifmissing of SignalsAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.SignalsAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.SignalsAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.SignalsAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.SignalsAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.SignalsAttr.ifmissing", "name": "ifmissing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.SignalsAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.SignalsAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ifmissing of SignalsAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.SignalsAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.SignalsAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.SignalsAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.SignalsAttr", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.SignalsAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.SignalsAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StarIgnoreAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.attributes.BaseAttr"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.StarIgnoreAttr", "name": "StarIgnoreAttr", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.StarIgnoreAttr", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.StarIgnoreAttr", "datastar_py.attributes.BaseAttr", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": ["self", "alias"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.StarIgnoreAttr.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": ["self", "alias"], "arg_types": ["datastar_py.attributes.StarIgnoreAttr", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StarIgnoreAttr", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "self": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.StarIgnoreAttr.self", "name": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.StarIgnoreAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.StarIgnoreAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "self of StarIgnoreAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.StarIgnoreAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.StarIgnoreAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.StarIgnoreAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.StarIgnoreAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.StarIgnoreAttr.self", "name": "self", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.StarIgnoreAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.StarIgnoreAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "self of StarIgnoreAttr", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.StarIgnoreAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.StarIgnoreAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.StarIgnoreAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.StarIgnoreAttr", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.StarIgnoreAttr.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.StarIgnoreAttr", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TAttr": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "name": "TAttr", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}}, "TimingMod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.TimingMod", "name": "<PERSON>ing<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.TimingMod", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.TimingMod", "builtins.object"], "names": {".class": "SymbolTable", "debounce": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "wait", "leading", "notrail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.TimingMod.debounce", "name": "debounce", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "wait", "leading", "notrail"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.TimingMod.debounce", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.str"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "debounce of TimingMod", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.TimingMod.debounce", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.TimingMod.debounce", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}]}}}, "throttle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "wait", "noleading", "trail"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.TimingMod.throttle", "name": "throttle", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "wait", "noleading", "trail"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.TimingMod.throttle", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.int", "builtins.float", "builtins.str"], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "throttle of TimingMod", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.TimingMod.throttle", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.TimingMod.throttle", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TimingMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.TimingMod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "ViewtransitionMod": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.attributes.ViewtransitionMod", "name": "ViewtransitionMod", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "datastar_py.attributes.ViewtransitionMod", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "datastar_py.attributes", "mro": ["datastar_py.attributes.ViewtransitionMod", "builtins.object"], "names": {".class": "SymbolTable", "viewtransition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "datastar_py.attributes.ViewtransitionMod.viewtransition", "name": "viewtransition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.ViewtransitionMod.viewtransition", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "viewtransition of ViewtransitionMod", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.ViewtransitionMod.viewtransition", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.ViewtransitionMod.viewtransition", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "datastar_py.attributes.ViewtransitionMod.viewtransition", "name": "viewtransition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.ViewtransitionMod.viewtransition", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "viewtransition of ViewtransitionMod", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.ViewtransitionMod.viewtransition", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.TAttr", "id": -1, "name": "TAttr", "namespace": "datastar_py.attributes.ViewtransitionMod.viewtransition", "upper_bound": "datastar_py.attributes.BaseAttr", "values": [], "variance": 0}]}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.attributes.ViewtransitionMod.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.attributes.ViewtransitionMod", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "datastar_py.attributes.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.attributes.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.attributes.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.attributes.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.attributes.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.attributes.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.attributes.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_escape": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes._escape", "name": "_escape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_escape", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_js_object": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "datastar_py.attributes._js_object", "name": "_js_object", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["obj"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_js_object", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "attribute_generator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "datastar_py.attributes.attribute_generator", "name": "attribute_generator", "type": "datastar_py.attributes.AttributeGenerator"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\datastar_py\\attributes.py"}
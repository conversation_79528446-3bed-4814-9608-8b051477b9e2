{".class": "MypyFile", "_fullname": "limits.storage", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ConfigurationError": {".class": "SymbolTableNode", "cross_ref": "limits.errors.ConfigurationError", "kind": "Gdef", "module_public": false}, "MemcachedStorage": {".class": "SymbolTableNode", "cross_ref": "limits.storage.memcached.MemcachedStorage", "kind": "Gdef"}, "MemoryStorage": {".class": "SymbolTableNode", "cross_ref": "limits.storage.memory.MemoryStorage", "kind": "Gdef"}, "MongoDBStorage": {".class": "SymbolTableNode", "cross_ref": "limits.storage.mongodb.MongoDBStorage", "kind": "Gdef"}, "MongoDBStorageBase": {".class": "SymbolTableNode", "cross_ref": "limits.storage.mongodb.MongoDBStorageBase", "kind": "Gdef"}, "MovingWindowSupport": {".class": "SymbolTableNode", "cross_ref": "limits.storage.base.MovingWindowSupport", "kind": "Gdef"}, "RedisClusterStorage": {".class": "SymbolTableNode", "cross_ref": "limits.storage.redis_cluster.RedisClusterStorage", "kind": "Gdef"}, "RedisSentinelStorage": {".class": "SymbolTableNode", "cross_ref": "limits.storage.redis_sentinel.RedisSentinelStorage", "kind": "Gdef"}, "RedisStorage": {".class": "SymbolTableNode", "cross_ref": "limits.storage.redis.RedisStorage", "kind": "Gdef"}, "SCHEMES": {".class": "SymbolTableNode", "cross_ref": "limits.storage.registry.SCHEMES", "kind": "Gdef", "module_public": false}, "SlidingWindowCounterSupport": {".class": "SymbolTableNode", "cross_ref": "limits.storage.base.SlidingWindowCounterSupport", "kind": "Gdef"}, "Storage": {".class": "SymbolTableNode", "cross_ref": "limits.storage.base.Storage", "kind": "Gdef"}, "StorageTypes": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "limits.storage.StorageTypes", "line": 23, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["limits.storage.base.Storage", "limits.aio.storage.base.Storage"], "uses_pep604_syntax": true}}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "limits.storage.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "limits": {".class": "SymbolTableNode", "cross_ref": "limits", "kind": "Gdef", "module_public": false}, "storage_from_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["storage_string", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.storage_from_string", "name": "storage_from_string", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["storage_string", "options"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.float", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "storage_from_string", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "limits.storage.StorageTypes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef", "module_public": false}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\storage\\__init__.py"}
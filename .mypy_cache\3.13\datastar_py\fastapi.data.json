{".class": "MypyFile", "_fullname": "datastar_py.fastapi", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "DatastarResponse": {".class": "SymbolTableNode", "cross_ref": "datastar_py.starlette.DatastarResponse", "kind": "Gdef"}, "Depends": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Depends", "kind": "Gdef", "module_public": false}, "ReadSignals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "datastar_py.fastapi.ReadSignals", "line": 18, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "SSE_HEADERS": {".class": "SymbolTableNode", "cross_ref": "datastar_py.sse.SSE_HEADERS", "kind": "Gdef"}, "ServerSentEventGenerator": {".class": "SymbolTableNode", "cross_ref": "datastar_py.sse.ServerSentEventGenerator", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "datastar_py.fastapi.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.fastapi.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.fastapi.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.fastapi.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.fastapi.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.fastapi.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.fastapi.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datastar_response": {".class": "SymbolTableNode", "cross_ref": "datastar_py.starlette.datastar_response", "kind": "Gdef"}, "read_signals": {".class": "SymbolTableNode", "cross_ref": "datastar_py.starlette.read_signals", "kind": "Gdef"}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\datastar_py\\fastapi.py"}
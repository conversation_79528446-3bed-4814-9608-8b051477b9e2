{"data_mtime": 1752571434, "dep_lines": [18, 17, 13, 14, 15, 16, 5, 7, 8, 9, 1, 1, 1, 1, 1, 1, 11], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 5], "dependencies": ["limits.aio.storage.base", "limits.aio.storage", "limits.limits", "limits.storage", "limits.typing", "limits.util", "__future__", "time", "abc", "math", "builtins", "_frozen_importlib", "limits.storage.base", "limits.storage.registry", "types", "typing"], "hash": "d412f59f92ca9d4e7ad7efa807f5ce9e5aba7b46", "id": "limits.aio.strategies", "ignore_all": true, "interface_hash": "438f9ff7faea3dda92d46c0f5d0da9b6b536506b", "mtime": 1752570205, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\aio\\strategies.py", "plugin_data": null, "size": 10736, "suppressed": ["deprecated.sphinx"], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "limits.aio.storage", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MemcachedStorage": {".class": "SymbolTableNode", "cross_ref": "limits.aio.storage.memcached.MemcachedStorage", "kind": "Gdef"}, "MemoryStorage": {".class": "SymbolTableNode", "cross_ref": "limits.aio.storage.memory.MemoryStorage", "kind": "Gdef"}, "MongoDBStorage": {".class": "SymbolTableNode", "cross_ref": "limits.aio.storage.mongodb.MongoDBStorage", "kind": "Gdef"}, "MovingWindowSupport": {".class": "SymbolTableNode", "cross_ref": "limits.aio.storage.base.MovingWindowSupport", "kind": "Gdef"}, "RedisClusterStorage": {".class": "SymbolTableNode", "cross_ref": "limits.aio.storage.redis.RedisClusterStorage", "kind": "Gdef"}, "RedisSentinelStorage": {".class": "SymbolTableNode", "cross_ref": "limits.aio.storage.redis.RedisSentinelStorage", "kind": "Gdef"}, "RedisStorage": {".class": "SymbolTableNode", "cross_ref": "limits.aio.storage.redis.RedisStorage", "kind": "Gdef"}, "SlidingWindowCounterSupport": {".class": "SymbolTableNode", "cross_ref": "limits.aio.storage.base.SlidingWindowCounterSupport", "kind": "Gdef"}, "Storage": {".class": "SymbolTableNode", "cross_ref": "limits.aio.storage.base.Storage", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "limits.aio.storage.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\aio\\storage\\__init__.py"}
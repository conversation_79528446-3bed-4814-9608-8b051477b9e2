{".class": "MypyFile", "_fullname": "slowapi.errors", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "HTTPException": {".class": "SymbolTableNode", "cross_ref": "starlette.exceptions.HTTPException", "kind": "Gdef"}, "Limit": {".class": "SymbolTableNode", "cross_ref": "slowapi.wrappers.Limit", "kind": "Gdef"}, "RateLimitExceeded": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["starlette.exceptions.HTTPException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "slowapi.errors.RateLimitExceeded", "name": "RateLimitExceeded", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "slowapi.errors.RateLimitExceeded", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "slowapi.errors", "mro": ["slowapi.errors.RateLimitExceeded", "starlette.exceptions.HTTPException", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "slowapi.errors.RateLimitExceeded.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "limit"], "arg_types": ["slowapi.errors.RateLimitExceeded", "slowapi.wrappers.Limit"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RateLimitExceeded", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "limit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "slowapi.errors.RateLimitExceeded.limit", "name": "limit", "type": {".class": "UnionType", "items": ["slowapi.wrappers.Limit", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "slowapi.errors.RateLimitExceeded.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "slowapi.errors.RateLimitExceeded", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "slowapi.errors.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "slowapi.errors.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "slowapi.errors.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "slowapi.errors.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "slowapi.errors.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "slowapi.errors.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\slowapi\\errors.py"}
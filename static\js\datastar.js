// Datastar v1.0.0-RC.1
var et=/🖕JS_DS🚀/.source,Te=et.slice(0,5),Ve=et.slice(4),F="datastar",tt="Datastar-Request",nt=1e3;var it=!1,Ne="outer",rt="inner",_e="remove",st="replace",ot="prepend",at="append",ct="before",lt="after",ut=Ne,ce="datastar-patch-elements",le="datastar-patch-signals";function Re(e){return e instanceof HTMLElement||e instanceof SVGElement}var Z=e=>e!==null&&typeof e=="object"&&(Object.getPrototypeOf(e)===Object.prototype||Object.getPrototypeOf(e)===null);function Ae(e){for(let t in e)if(Object.hasOwn(e,t))return!1;return!0}function ue(e,t){for(let n in e){let i=e[n];Z(i)||Array.isArray(i)?ue(i,t):e[n]=t(i)}}var m=(e,t)=>{for(let n in t){let i=n.split("."),r=i.pop(),o=i.reduce((s,a)=>s[a]??={},e);o[r]=t[n]}return e};var ft=e=>e.trim()==="true",x=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").replace(/([a-z])([0-9]+)/gi,"$1-$2").replace(/([0-9]+)([a-z])/gi,"$1-$2").toLowerCase(),X=e=>x(e).replace(/-./g,t=>t[1].toUpperCase()),fe=e=>x(e).replace(/-/g,"_"),Tn=e=>X(e).replace(/(^.|(?<=\.).)/g,t=>t[0].toUpperCase()),ee=e=>{try{return JSON.parse(e)}catch{return Function(`return (${e})`)()}},Rn={kebab:x,snake:fe,pascal:Tn};function L(e,t){for(let n of t.get("case")||[]){let i=Rn[n];i&&(e=i(e))}return e}var An="https://data-star.dev/errors";function dt(e,t,n={}){let i=new Error;i.name=`${F} ${e} error`;let r=fe(t),o=new URLSearchParams({metadata:JSON.stringify(n)}).toString(),s=JSON.stringify(n,null,2);return i.message=`${t}
More info: ${An}/${e}/${r}?${o}
Context: ${s}`,i}function K(e,t,n={}){let i={plugin:{name:t.plugin.name,type:t.plugin.type}};return dt("init",e,Object.assign(i,n))}function pt(e,t,n={}){let i={plugin:{name:e.plugin.name,type:e.plugin.type},element:{id:e.el.id,tag:e.el.tagName},expression:{rawKey:e.rawKey,key:e.key,value:e.value,fnContent:e.fnContent}};return dt("runtime",t,Object.assign(i,n))}var U=`${F}-signal-patch`;var xe={},je=[],Pe=0,Le=0,Ge=0,H,ge=()=>{Pe++},ve=()=>{--Pe||(bt(),I())},te=e=>Ln.bind(0,{previousValue:e,t:e,e:1}),vt=Symbol("computed"),yt=e=>{let t=xn.bind(0,{e:17,getter:e});return t[vt]=1,t},ht=e=>{let t={d:e,e:2};H&&Ue(t,H);let n=V(t);ge();try{t.d()}finally{ve(),V(n)}return Rt.bind(0,t)},We=e=>{let t=V(void 0);try{return e()}finally{V(t)}},bt=()=>{for(;Le<Ge;){let e=je[Le];je[Le++]=void 0,Tt(e,e.e&=-65)}Le=0,Ge=0},mt=e=>"getter"in e?Et(e):St(e,e.t),V=e=>{let t=H;return H=e,t},Et=e=>{let t=V(e);At(e);try{let n=e.t;return n!==(e.t=e.getter(n))}finally{V(t),xt(e)}},St=(e,t)=>(e.e=1,e.previousValue!==(e.previousValue=t)),Ke=e=>{let t=e.e;if(!(t&64)){e.e=t|64;let n=e.s;n?Ke(n.o):je[Ge++]=e}},Tt=(e,t)=>{if(t&16||t&32&&Lt(e.r,e)){let i=V(e);At(e),ge();try{e.d()}finally{ve(),V(i),xt(e)}return}t&32&&(e.e=t&-33);let n=e.r;for(;n;){let i=n.c,r=i.e;r&64&&Tt(i,i.e=r&-65),n=n.n}},xn=e=>{let t=e.e;if(t&16||t&32&&Lt(e.r,e)){if(Et(e)){let n=e.s;n&&Ce(n)}}else t&32&&(e.e=t&-33);return H&&Ue(e,H),e.t},Ln=(e,...t)=>{if(t.length){let i=t[0];if(e.t!==(e.t=i)){e.e=17;let r=e.s;return r&&(Mn(r),Pe||bt()),!0}return!1}let n=e.t;if(e.e&16&&St(e,n)){let i=e.s;i&&Ce(i)}return H&&Ue(e,H),n},Rt=e=>{let t=e.r;for(;t;)t=Oe(t,e);let n=e.s;n&&Oe(n),e.e=0},Ue=(e,t)=>{let n=t.a;if(n&&n.c===e)return;let i,r=t.e&4;if(r&&(i=n?n.n:t.r,i&&i.c===e)){t.a=i;return}let o=e.p;if(o&&o.o===t&&(!r||Mt(o,t)))return;let s=t.a=e.p={c:e,o:t,l:n,n:i,u:o};i&&(i.l=s),n?n.n=s:t.r=s,o?o.i=s:e.s=s},Oe=(e,t=e.o)=>{let n=e.c,i=e.l,r=e.n,o=e.i,s=e.u;if(r?r.l=i:t.a=i,i?i.n=r:t.r=r,o?o.u=s:n.p=s,s)s.i=o;else if(!(n.s=o))if("getter"in n){let a=n.r;if(a){n.e=17;do a=Oe(a,n);while(a)}}else"previousValue"in n||Rt(n);return r},Mn=e=>{let t=e.i,n;e:for(;;){let i=e.o,r=i.e;if(r&3&&(r&60?r&12?r&4?!(r&48)&&Mt(e,i)?(i.e=r|40,r&=1):r=0:i.e=r&-9|32:r=0:i.e=r|32,r&2&&Ke(i),r&1)){let o=i.s;if(o){e=o,o.i&&(n={t,f:n},t=e.i);continue}}if(e=t){t=e.i;continue}for(;n;)if(e=n.t,n=n.f,e){t=e.i;continue e}break}},At=e=>{e.a=void 0,e.e=e.e&-57|4},xt=e=>{let t=e.a,n=t?t.n:e.r;for(;n;)n=Oe(n,e);e.e&=-5},Lt=(e,t)=>{let n,i=0;e:for(;;){let r=e.c,o=r.e,s=!1;if(t.e&16)s=!0;else if((o&17)===17){if(mt(r)){let a=r.s;a.i&&Ce(a),s=!0}}else if((o&33)===33){(e.i||e.u)&&(n={t:e,f:n}),e=r.r,t=r,++i;continue}if(!s&&e.n){e=e.n;continue}for(;i;){--i;let a=t.s,c=a.i;if(c?(e=n.t,n=n.f):e=a,s){if(mt(t)){c&&Ce(a),t=e.o;continue}}else t.e&=-33;if(t=e.o,e.n){e=e.n;continue e}s=!1}return s}},Ce=e=>{do{let t=e.o,n=e.i,i=t.e;(i&48)===32&&(t.e=i|16,i&2&&Ke(t)),e=n}while(e)},Mt=(e,t)=>{let n=t.a;if(n){let i=t.r;do{if(i===e)return!0;if(i===n)break;i=i.n}while(i)}return!1},Be=e=>e.split(".").reduce((t,n)=>t[n],N),wt=e=>We(()=>e.split(".").reduce((t,n)=>t&&Object.hasOwn(t,n)?t[n]:void 0,N)!==void 0),gt=Symbol("delete"),Me=(e,t="")=>{let n=Array.isArray(e);if(n||Z(e)){let i=n?[]:{};for(let o in e)i[o]=te(Me(e[o],`${t+o}.`));let r=te(0);return new Proxy(i,{get:(o,s)=>{if(!(s==="toJSON"&&!Object.hasOwn(i,s)))return n&&s in Array.prototype?(r(),i[s]):((!Object.hasOwn(i,s)||i[s]()==null)&&(i[s]=te(""),I({[t+s]:""}),r(r()+1)),i[s]())},set:(o,s,a)=>(a===gt?Object.hasOwn(i,s)&&(delete i[s],I({[t+s]:gt}),r(r()+1)):n&&s==="length"?(i[s]=a,I({[t.slice(0,-1)]:i}),r(r()+1)):Object.hasOwn(i,s)?a==null?i[s](null)&&I({[t+s]:null}):i[s](Me(a,`${t+s}.`))&&I({[t+s]:a}):a!=null&&(Object.hasOwn(a,vt)?(i[s]=a,I({[t+s]:""})):(i[s]=te(Me(a,`${t+s}.`)),I({[t+s]:a})),r(r()+1)),!0),deleteProperty:(o,s)=>(Object.hasOwn(i,s)&&i[s](null)&&I({[t+s]:null}),!0),ownKeys:()=>(r(),Reflect.ownKeys(i)),has(o,s){return r(),s in i}})}return e},I=e=>{if(e&&m(xe,e),!Pe&&!Ae(xe)){let t=xe;xe={},document.dispatchEvent(new CustomEvent(U,{detail:t}))}},Ot=(e,{ifMissing:t}={})=>{ge();for(let n in e)e[n]==null?t||delete N[n]:Ct(e[n],n,N,"",t);ve()},Ct=(e,t,n,i,r)=>{if(Z(e)){Object.hasOwn(n,t)&&(Z(n[t])||Array.isArray(n[t]))||(n[t]={});for(let o in e)e[o]==null?r||delete n[t][o]:Ct(e[o],o,n[t],`${i+t}.`,r)}else r&&Object.hasOwn(n,t)||(n[t]=e)};function Ft({include:e=/.*/,exclude:t=/(?!)/}={},n=N){let i={},r=[[n,""]];for(;r.length;){let[o,s]=r.pop();for(let a in o)Z(o[a])?r.push([o[a],`${s+a}.`]):e.test(s+a)&&!t.test(s+a)&&(i[s+a]=Be(s+a))}return m({},i)}var N=Me({}),pe={},we=[],Pt=[],de=new Map,qe=null,me="";function kt(e){me=e}function w(e){return me?`data-${me}-${e}`:`data-${e}`}function Je(...e){for(let t of e){let n={plugin:t,actions:pe,root:N,filtered:Ft,signal:te,computed:yt,effect:ht,mergePatch:Ot,peek:We,getPath:Be,hasPath:wt,startBatch:ge,endBatch:ve};if(t.type==="action")pe[t.name]=t;else if(t.type==="attribute")we.push(t),t.onGlobalInit?.(n);else if(t.type==="watcher")t.onGlobalInit?.(n);else throw K("InvalidPluginType",n)}we.sort((t,n)=>{let i=n.name.length-t.name.length;return i!==0?i:t.name.localeCompare(n.name)}),Pt=we.map(t=>RegExp(`^${t.name}([A-Z]|_|$)`))}function Fe(e){let t=`[${w("ignore")}]`;for(let n of e)if(!n.closest(t))for(let i in n.dataset)Dt(n,i,n.dataset[i])}function ze(e=document.body){queueMicrotask(()=>{Fe([e]),Fe(e.querySelectorAll("*")),qe||(qe=new MutationObserver(wn),qe.observe(e,{subtree:!0,childList:!0,attributes:!0}))})}function Dt(e,t,n){let i=X(me?t.slice(me.length):t),r=we.find((o,s)=>Pt[s].test(i));if(r){let[o,...s]=i.slice(r.name.length).split(/__+/),a=!!o;a&&(o=X(o));let c=!!n,l={plugin:r,actions:pe,root:N,filtered:Ft,signal:te,computed:yt,effect:ht,mergePatch:Ot,peek:We,getPath:Be,hasPath:wt,startBatch:ge,endBatch:ve,el:e,rawKey:i,key:o,value:n,mods:new Map,runtimeErr:0,rx:0};l.runtimeErr=pt.bind(0,l),(r.shouldEvaluate===void 0||r.shouldEvaluate===!0)&&(l.rx=On(l));let d=r.keyReq||"allowed";if(a){if(d==="denied")throw l.runtimeErr(`${r.name}KeyNotAllowed`)}else if(d==="must")throw l.runtimeErr(`${r.name}KeyRequired`);let f=r.valReq||"allowed";if(c){if(f==="denied")throw l.runtimeErr(`${r.name}ValueNotAllowed`)}else if(f==="must")throw l.runtimeErr(`${r.name}ValueRequired`);if(d==="exclusive"||f==="exclusive"){if(a&&c)throw l.runtimeErr(`${r.name}KeyAndValueProvided`);if(!a&&!c)throw l.runtimeErr(`${r.name}KeyOrValueRequired`)}for(let p of s){let[h,...g]=p.split(".");l.mods.set(X(h),new Set(g.map(y=>y.toLowerCase())))}let u=r.onLoad(l);if(u){let p=de.get(e);p?p.get(i)?.():(p=new Map,de.set(e,p)),p.set(i,u)}}}function wn(e){let t=`[${w("ignore")}]`;for(let{target:n,type:i,attributeName:r,addedNodes:o,removedNodes:s}of e)if(i==="childList"){for(let a of s)if(Re(a)){let c=de.get(a);if(de.delete(a)){for(let l of c.values())l();c.clear()}}for(let a of o)Re(a)&&(Fe([a]),Fe(a.querySelectorAll("*")))}else if(i==="attributes"&&Re(n)&&!n.closest(t)){let a=X(r.slice(5)),c=n.getAttribute(r);if(c===null){let l=de.get(n);l&&(l.get(a)?.(),l.delete(a))}else Dt(n,a,c)}}function On(e){let t="",n=e.plugin||void 0;if(n?.returnsValue){let f=/(\/(\\\/|[^/])*\/|"(\\"|[^"])*"|'(\\'|[^'])*'|`(\\`|[^`])*`|\(\s*((function)\s*\(\s*\)|(\(\s*\))\s*=>)\s*(?:\{[\s\S]*?\}|[^;){]*)\s*\)\s*\(\s*\)|[^;])+/gm,u=e.value.trim().match(f);if(u){let p=u.length-1,h=u[p].trim();h.startsWith("return")||(u[p]=`return (${h});`),t=u.join(`;
`)}}else t=e.value.trim();t=t.replace(/\$([\w.-]+(?:\.[\w.-]+)*?)(?=\s|$|[^\w.-])/g,(f,u)=>u.endsWith("-")&&f.length<t.length&&t[t.indexOf(f)+f.length]==="$"?(u=u.slice(0,-1),`${u.split(".").reduce((g,y)=>`${g}['${y}']`,"$")}-`):u.split(".").reduce((h,g)=>`${h}['${g}']`,"$"));let i=new Map,r=RegExp(`(?:${Te})(.*?)(?:${Ve})`,"gm");for(let f of t.matchAll(r)){let u=f[1],p=`dsEscaped${Cn(u)}`;i.set(p,u),t=t.replace(Te+u+Ve,p)}let o=(f,u)=>`${f}${fe(u).replaceAll(/\./g,"_")}`,s=new Set,a=RegExp(`@(${Object.keys(pe).join("|")})\\(`,"gm"),c=[...t.matchAll(a)],l=new Set,d=new Set;if(c.length){let f=`${F}Act_`;for(let u of c){let p=u[1],h=pe[p];if(!h)continue;s.add(p);let g=o(f,p);t=t.replace(`@${p}(`,`${g}(`),l.add(g),d.add((...y)=>h.fn(e,...y))}}for(let[f,u]of i)t=t.replace(f,u);e.fnContent=t;try{let f=Function("el","$",...n?.argNames||[],...l,t);return(...u)=>{try{return f(e.el,N,...u,...d)}catch(p){throw e.runtimeErr("ExecuteExpression",{error:p.message})}}}catch(f){throw e.runtimeErr("GenerateExpression",{error:f.message})}}function Cn(e){let t=5831,n=e.length;for(;n--;)t+=(t<<5)+e.charCodeAt(n);return(t>>>0).toString(36)}var $t={type:"action",name:"peek",fn:({peek:e},t)=>e(t)};var It={type:"action",name:"setAll",fn:({filtered:e,mergePatch:t,peek:n},i,r)=>{n(()=>{let o=e(r);ue(o,()=>i),t(o)})}};var Ht={type:"action",name:"toggleAll",fn:({filtered:e,mergePatch:t,peek:n},i)=>{n(()=>{let r=e(i);ue(r,o=>!o),t(r)})}};var Vt={type:"attribute",name:"attr",valReq:"must",returnsValue:!0,onLoad:({el:e,effect:t,key:n,rx:i})=>{let r=(c,l)=>{l===""||l===!0?e.setAttribute(c,""):l===!1||l===null||l===void 0?e.removeAttribute(c):e.setAttribute(c,l)};if(n===""){let c=new MutationObserver(()=>{c.disconnect();let d=i();for(let[f,u]of Object.entries(d))r(f,u);c.observe(e,{attributeFilter:Object.keys(d)})}),l=t(()=>{c.disconnect();let d=i();for(let f in d)r(f,d[f]);c.observe(e,{attributeFilter:Object.keys(d)})});return()=>{c.disconnect(),l()}}let o=x(n),s=new MutationObserver(()=>{s.disconnect();let c=i();r(o,c),s.observe(e,{attributeFilter:[c]})}),a=t(()=>{s.disconnect();let c=i();r(o,c),s.observe(e,{attributeFilter:[c]})});return()=>{s.disconnect(),a()}}};var Fn=/^data:(?<mime>[^;]+);base64,(?<contents>.*)$/,Pn=/email|password|search|tel|text|url/,kn=/number|range/,Nt={type:"attribute",name:"bind",keyReq:"exclusive",valReq:"exclusive",shouldEvaluate:!1,onLoad:({el:e,key:t,mods:n,value:i,effect:r,mergePatch:o,runtimeErr:s,getPath:a,hasPath:c})=>{let l=t?L(t,n):i;if(e instanceof HTMLInputElement&&Pn.test(e.type)||e instanceof HTMLTextAreaElement){if(Array.isArray(c(l)&&a(l))){let h=document.querySelectorAll(`[${w("bind")}-${t}],[${w("bind")}="${i}"]`),g=0,y={};for(let T of h){if(c(`${l}.${g}`)||(y[`${l}.${g}`]=T.value),e===T)break;g++}o(m({},y));let b=()=>{o(m({},{[`${l}.${g}`]:e.value}))};e.addEventListener("change",b),e.addEventListener("input",b);let R=r(()=>e.value=a(l)[g]);return()=>{R(),e.removeEventListener("change",b),e.removeEventListener("input",b)}}o(m({},{[l]:e.value}),{ifMissing:!0});let u=()=>o(m({},{[l]:e.value}));e.addEventListener("change",u),e.addEventListener("input",u);let p=r(()=>e.value=a(l));return()=>{p(),e.removeEventListener("change",u),e.removeEventListener("input",u)}}if(e instanceof HTMLInputElement){if(e.type==="checkbox"){if(Array.isArray(c(l)&&a(l))){let g=document.querySelectorAll(`[${w("bind")}-${t}],[${w("bind")}="${i}"]`),y=0,b={};for(let S of g){if(!c(`${l}.${y}`)){let P=S.getAttribute("value");b[`${l}.${y}`]=P?S.checked?P:"":S.checked}if(e===S)break;y++}o(m({},b));let R=()=>{let S=e.getAttribute("value");o(m({},{[`${l}.${y}`]:S?e.checked?S:"":e.checked}))};e.addEventListener("change",R),e.addEventListener("input",R);let T=r(()=>{let S=e.getAttribute("value");e.checked=S?S===a(l)[y]:a(l)[y]});return()=>{T(),e.removeEventListener("change",R),e.removeEventListener("input",R)}}let u=e.getAttribute("value");o(m({},{[l]:u?e.checked?u:"":e.checked}));let p=()=>{let g=e.getAttribute("value");o(m({},{[l]:g?e.checked?g:"":e.checked}))};e.addEventListener("change",p),e.addEventListener("input",p);let h=r(()=>{let g=e.getAttribute("value");e.checked=g?g===a(l):a(l)});return()=>{h(),e.removeEventListener("change",p),e.removeEventListener("input",p)}}if(e.type==="radio"){e.getAttribute("name")?.length||e.setAttribute("name",l),o(m({},{[l]:e.value}),{ifMissing:!0});let u=()=>e.checked&&o(m({},{[l]:e.value}));e.addEventListener("change",u),e.addEventListener("input",u);let p=r(()=>e.checked=e.value===a(l));return()=>{p(),e.removeEventListener("change",u),e.removeEventListener("input",u)}}if(kn.test(e.type)){o(m({},{[l]:+e.value}),{ifMissing:!0});let u=()=>o(m({},{[l]:+e.value}));e.addEventListener("change",u),e.addEventListener("input",u);let p=r(()=>e.value=a(l));return()=>{p(),e.removeEventListener("change",u),e.removeEventListener("input",u)}}if(e.type==="file"){let u=()=>{let p=[...e.files||[]],h=[],g=[],y=[];Promise.all(p.map(b=>new Promise(R=>{let T=new FileReader;T.onload=()=>{if(typeof T.result!="string")throw s("InvalidFileResultType",{resultType:typeof T.result});let S=T.result.match(Fn);if(!S?.groups)throw s("InvalidDataUri",{result:T.result});h.push(S.groups.contents),g.push(S.groups.mime),y.push(b.name)},T.onloadend=()=>R(),T.readAsDataURL(b)}))).then(()=>{o(m({},{[l]:h,[`${l}Mimes`]:g,[`${l}Names`]:y}))})};return e.addEventListener("change",u),e.addEventListener("input",u),()=>{e.removeEventListener("change",u),e.removeEventListener("input",u)}}}if(e instanceof HTMLSelectElement){if(e.multiple){o(m({},{[l]:[...e.selectedOptions].map(b=>b.value)}),{ifMissing:!0});let g=()=>o(m({},{[l]:[...e.selectedOptions].map(b=>b.value)}));e.addEventListener("change",g),e.addEventListener("input",g);let y=r(()=>{let b=a(l);for(let R of e.options)R.selected=b.includes(R.value)});return()=>{y(),e.removeEventListener("change",g),e.removeEventListener("input",g)}}o(m({},{[l]:e.value}),{ifMissing:!0});let u=()=>o(m({},{[l]:e.value}));e.addEventListener("change",u),e.addEventListener("input",u);let h=r(()=>e.value=a(l));return()=>{h(),e.removeEventListener("change",u),e.removeEventListener("input",u)}}o(m({},{[l]:e.getAttribute("value")}),{ifMissing:!0});let d=r(()=>{let u=a(l);e.getAttribute("value")!==u&&e.setAttribute("value",u)}),f=u=>o(m({},{[l]:u.target?.value}));return e.addEventListener("change",f),e.addEventListener("input",f),()=>{d(),e.removeEventListener("change",f),e.removeEventListener("input",f)}}};var _t={type:"attribute",name:"class",valReq:"must",returnsValue:!0,onLoad:({key:e,el:t,effect:n,mods:i,rx:r})=>{e&&(e=L(x(e),i));let o=()=>{s.disconnect();let c=e?{[e]:r()}:r();for(let l in c){let d=l.split(/\s+/).filter(f=>f.length>0);if(c[l])for(let f of d)t.classList.add(f);else for(let f of d)t.classList.remove(f)}s.observe(t,{attributeFilter:["class"]})},s=new MutationObserver(o),a=n(o);return()=>{s.disconnect(),a();let c=e?{[e]:r()}:r();for(let l in c){let d=l.split(/\s+/).filter(f=>f.length>0);for(let f of d)t.classList.remove(f)}}}};var qt={type:"attribute",name:"computed",keyReq:"must",valReq:"must",returnsValue:!0,onLoad:({key:e,mods:t,rx:n,computed:i,mergePatch:r})=>{r(m({},{[L(e,t)]:i(n)}))}};var jt={type:"attribute",name:"effect",keyReq:"denied",valReq:"must",onLoad:({effect:e,rx:t})=>e(t)};var _=`${F}-sse`,ke="started",De="finished",Gt="error",Wt="retrying",Kt="retrying";function $e(e,t){document.addEventListener(_,n=>{if(n.detail.type===e){let{argsRaw:i}=n.detail;t(i)}})}var Ut={type:"attribute",name:"indicator",keyReq:"exclusive",valReq:"exclusive",shouldEvaluate:!1,onLoad:({el:e,key:t,mods:n,mergePatch:i,value:r})=>{let o=t?L(t,n):r;i(m({},{[o]:!1}),{ifMissing:!0});let s=a=>{let{type:c,el:l}=a.detail;if(l===e)switch(c){case ke:i(m({},{[o]:!0}));break;case De:i(m({},{[o]:!1}));break}};return document.addEventListener(_,s),()=>{i(m({},{[o]:!1})),document.removeEventListener(_,s)}}};var Bt={type:"attribute",name:"jsonSignals",keyReq:"denied",onLoad:({el:e,effect:t,value:n,filtered:i,mods:r})=>{let o=r.has("terse")?0:2,s={};n&&(s=ee(n));let a=()=>{c.disconnect(),e.textContent=JSON.stringify(i(s),null,o),c.observe(e,{childList:!0})},c=new MutationObserver(a),l=t(a);return()=>{c.disconnect(),l()}}};function q(e){if(!e||e.size<=0)return 0;for(let t of e){if(t.endsWith("ms"))return+t.replace("ms","");if(t.endsWith("s"))return+t.replace("s","")*1e3;try{return Number.parseFloat(t)}catch{}}return 0}function B(e,t,n=!1){return e?e.has(t.toLowerCase()):n}function Qe(e,t){return(...n)=>{setTimeout(()=>{e(...n)},t)}}function Dn(e,t,n=!1,i=!0){let r=0;return(...o)=>{r&&clearTimeout(r),n&&!r&&e(...o),r=setTimeout(()=>{i&&e(...o),r&&clearTimeout(r)},t)}}function $n(e,t,n=!0,i=!1){let r=!1;return(...o)=>{r||(n&&e(...o),r=!0,setTimeout(()=>{r=!1,i&&e(...o)},t))}}function ne(e,t){let n=t.get("delay");if(n){let o=q(n);e=Qe(e,o)}let i=t.get("debounce");if(i){let o=q(i),s=B(i,"leading",!1),a=!B(i,"notrail",!1);e=Dn(e,o,s,a)}let r=t.get("throttle");if(r){let o=q(r),s=!B(r,"noleading",!1),a=B(r,"trail",!1);e=$n(e,o,s,a)}return e}var Ie=!!document.startViewTransition;function j(e,t){if(t.has("viewtransition")&&Ie){let n=e;e=(...i)=>document.startViewTransition(()=>n(...i))}return e}var Jt={type:"attribute",name:"on",keyReq:"must",valReq:"must",argNames:["evt"],onLoad:e=>{let{el:t,key:n,mods:i,rx:r,startBatch:o,endBatch:s}=e,a=t;i.has("window")&&(a=window);let c=f=>{if(f){if(i.has("prevent")&&f.preventDefault(),i.has("stop")&&f.stopPropagation(),!(f.isTrusted||f instanceof CustomEvent||i.has("trusted")))return;e.evt=f}o(),r(f),s()};c=ne(c,i),c=j(c,i);let l={capture:i.has("capture"),passive:i.has("passive"),once:i.has("once")};if(i.has("outside")){a=document;let f=c;c=u=>{t.contains(u?.target)||f(u)}}let d=x(n);if(d=L(d,i),(d===_||d===U)&&(a=document),t instanceof HTMLFormElement&&d==="submit"){let f=c;c=u=>{u?.preventDefault(),f(u)}}return a.addEventListener(d,c,l),()=>{a.removeEventListener(d,c)}}};var Ye=new WeakSet,zt={type:"attribute",name:"onIntersect",keyReq:"denied",onLoad:({el:e,mods:t,rx:n,startBatch:i,endBatch:r})=>{let o=()=>{i(),n(),r()};o=ne(o,t),o=j(o,t);let s={threshold:0};t.has("full")?s.threshold=1:t.has("half")&&(s.threshold=.5);let a=new IntersectionObserver(c=>{for(let l of c)l.isIntersecting&&(o(),a&&Ye.has(e)&&a.disconnect())},s);return a.observe(e),t.has("once")&&Ye.add(e),()=>{t.has("once")||Ye.delete(e),a&&(a.disconnect(),a=null)}}};var Qt={type:"attribute",name:"onInterval",keyReq:"denied",valReq:"must",onLoad:({mods:e,rx:t,startBatch:n,endBatch:i})=>{let r=()=>{n(),t(),i()};r=j(r,e);let o=1e3,s=e.get("duration");s&&(o=q(s),B(s,"leading",!1)&&r());let a=setInterval(r,o);return()=>{clearInterval(a)}}};var Yt={type:"attribute",name:"onLoad",keyReq:"denied",valReq:"must",onLoad:({rx:e,mods:t,startBatch:n,endBatch:i})=>{let r=()=>{n(),e(),i()};r=j(r,t);let o=0,s=t.get("delay");s&&(o=q(s)),r=Qe(r,o),r()}};var Zt={type:"attribute",name:"onSignalPatch",valReq:"must",argNames:["patch"],returnsValue:!0,onLoad:({el:e,key:t,mods:n,plugin:i,rx:r,filtered:o,runtimeErr:s,startBatch:a,endBatch:c})=>{if(t&&t!=="filter")throw s(`${i.name}KeyNotAllowed`);let l=e.getAttribute("data-on-signal-patch-filter"),d={};l&&(d=ee(l));let f=ne(u=>{let p=o(d,u.detail);Ae(p)||(a(),r(p),c())},n);return document.addEventListener(U,f),()=>{document.removeEventListener(U,f)}}};var Xt={type:"attribute",name:"ref",keyReq:"exclusive",valReq:"exclusive",shouldEvaluate:!1,onLoad:({el:e,key:t,mods:n,value:i,mergePatch:r})=>{let o=t?L(t,n):i;r(m({},{[o]:e}))}};var en="none",tn="display",nn={type:"attribute",name:"show",keyReq:"denied",valReq:"must",returnsValue:!0,onLoad:({el:e,effect:t,rx:n})=>{let i=()=>{r.disconnect(),n()?e.style.display===en&&e.style.removeProperty(tn):e.style.setProperty(tn,en),r.observe(e,{attributeFilter:["style"]})},r=new MutationObserver(i),o=t(i);return()=>{r.disconnect(),o()}}};var rn={type:"attribute",name:"signals",returnsValue:!0,onLoad:({key:e,mods:t,rx:n,mergePatch:i})=>{let r=t.has("ifmissing");if(e)e=L(e,t),i(m({},{[e]:n()}),{ifMissing:r});else{let o=n(),s={};for(let a in o)s[a]=o[a];i(m({},s),{ifMissing:r})}}};var sn={type:"attribute",name:"text",keyReq:"denied",valReq:"must",returnsValue:!0,onLoad:({el:e,effect:t,rx:n})=>{let i=()=>{r.disconnect(),e.textContent=`${n()}`,r.observe(e,{childList:!0})},r=new MutationObserver(i),o=t(i);return()=>{r.disconnect(),o()}}};var He=new WeakMap,$=(e,t)=>({type:"action",name:e,fn:async(n,i,r)=>{let{el:o}=n;He.get(o)?.abort();let s=new AbortController;He.set(o,s);try{await In(n,t,i,r,s.signal)}finally{He.get(o)===s&&He.delete(o)}}}),J=(e,t,n)=>document.dispatchEvent(new CustomEvent(_,{detail:{type:e,el:t,argsRaw:n}})),on=e=>`${e}`.includes("text/event-stream"),In=async({el:e,evt:t,filtered:n,runtimeErr:i},r,o,{selector:s,headers:a,contentType:c="json",filterSignals:l={include:/.*/,exclude:/(^|\.)_/},openWhenHidden:d=!1,retryInterval:f=nt,retryScaler:u=2,retryMaxWaitMs:p=3e4,retryMaxCount:h=10}={},g)=>{let y=r.toLowerCase(),b=()=>{};try{if(!o?.length)throw i("SseNoUrlProvided",{action:y});let R={Accept:"text/event-stream, text/html, application/json",[tt]:!0};c==="json"&&(R["Content-Type"]="application/json");let T=Object.assign({},R,a),S={method:r,headers:T,openWhenHidden:d,retryInterval:f,retryScaler:u,retryMaxWaitMs:p,retryMaxCount:h,signal:g,onopen:async v=>{v.status>=400&&J(Gt,e,{status:v.status.toString()})},onmessage:v=>{if(!v.event.startsWith(F))return;let G=v.event,D={};for(let M of v.data.split(`
`)){let E=M.indexOf(" "),A=M.slice(0,E),z=M.slice(E+1);(D[A]||=[]).push(z)}let W=Object.fromEntries(Object.entries(D).map(([M,E])=>[M,E.join(`
`)]));J(G,e,W)},onerror:v=>{if(on(v))throw i("InvalidContentType",{url:o});v&&(console.error(v.message),J(Wt,e,{message:v.message}))}},P=new URL(o,window.location.href),k=new URLSearchParams(P.search);if(c==="json"){let v=JSON.stringify(n(l));r==="GET"?k.set(F,v):S.body=v}else if(c==="form"){let v=s?document.querySelector(s):e.closest("form");if(!v)throw i(s?"SseFormNotFound":"SseClosestFormNotFound",{action:y,selector:s});if(!v.checkValidity()){v.reportValidity(),b();return}let G=new FormData(v),D=e;if(e===v&&t instanceof SubmitEvent)D=t.submitter;else{let E=A=>A.preventDefault();v.addEventListener("submit",E),b=()=>v.removeEventListener("submit",E)}if(D instanceof HTMLButtonElement){let E=D.getAttribute("name");E&&G.append(E,D.value)}let W=v.getAttribute("enctype")==="multipart/form-data";W||(T["Content-Type"]="application/x-www-form-urlencoded");let M=new URLSearchParams(G);if(r==="GET")for(let[E,A]of M)k.append(E,A);else W?S.body=G:S.body=M}else throw i("SseInvalidContentType",{action:y,contentType:c});J(ke,e,{}),P.search=k.toString();try{await qn(P.toString(),e,S)}catch(v){if(!on(v))throw i("SseFetchFailed",{method:r,url:o,error:v})}}finally{J(De,e,{}),b()}};async function Hn(e,t){let n=e.getReader(),i=await n.read();for(;!i.done;)t(i.value),i=await n.read()}function Vn(e){let t,n,i,r=!1;return function(s){t?t=_n(t,s):(t=s,n=0,i=-1);let a=t.length,c=0;for(;n<a;){r&&(t[n]===10&&(c=++n),r=!1);let l=-1;for(;n<a&&l===-1;++n)switch(t[n]){case 58:i===-1&&(i=n-c);break;case 13:r=!0;case 10:l=n;break}if(l===-1)break;e(t.subarray(c,l),i),c=n,i=-1}c===a?t=void 0:c&&(t=t.subarray(c),n-=c)}}function Nn(e,t,n){let i=an(),r=new TextDecoder;return function(s,a){if(!s.length)n?.(i),i=an();else if(a>0){let c=r.decode(s.subarray(0,a)),l=a+(s[a+1]===32?2:1),d=r.decode(s.subarray(l));switch(c){case"data":i.data=i.data?`${i.data}
${d}`:d;break;case"event":i.event=d;break;case"id":e(i.id=d);break;case"retry":{let f=+d;Number.isNaN(f)||t(i.retry=f);break}}}}}var _n=(e,t)=>{let n=new Uint8Array(e.length+t.length);return n.set(e),n.set(t,e.length),n},an=()=>({data:"",event:"",id:"",retry:void 0});function qn(e,t,{signal:n,headers:i,onopen:r,onmessage:o,onclose:s,onerror:a,openWhenHidden:c,fetch:l,retryInterval:d=1e3,retryScaler:f=2,retryMaxWaitMs:u=3e4,retryMaxCount:p=10,overrides:h,...g}){return new Promise((y,b)=>{let R={accept:"text/event-stream",...i},T;function S(){T.abort(),document.hidden||M()}c||document.addEventListener("visibilitychange",S);let P=0;function k(){document.removeEventListener("visibilitychange",S),window.clearTimeout(P),T.abort()}n?.addEventListener("abort",()=>{k(),y()});let v=l||window.fetch,G=r||(()=>{}),D=0,W=d;async function M(){T=new AbortController;try{let E=await v(e,{...g,headers:R,signal:T.signal});D=0,d=W,await G(E);let A=async(C,Q,oe,Y,...he)=>{let be={[oe]:await Q.text()};for(let Ee of he){let Se=Q.headers.get(`datastar-${x(Ee)}`);if(Y){let ae=Y[Ee];ae&&(Se=typeof ae=="string"?ae:JSON.stringify(ae))}Se&&(be[Ee]=Se)}J(C,t,be),k()},z=E.headers.get("Content-Type");if(z?.includes("text/html"))return await A(ce,E,"elements",h,"selector","mode","useViewTransition");if(z?.includes("application/json"))return await A(le,E,"signals",h,"onlyIfMissing");if(z?.includes("text/javascript")){let C=document.createElement("script"),Q=E.headers.get("datastar-script-attributes");if(Q)for(let[oe,Y]of Object.entries(JSON.parse(Q)))C.setAttribute(oe,Y);C.textContent=await E.text(),document.head.appendChild(C),k();return}await Hn(E.body,Vn(Nn(C=>{C?R["last-event-id"]=C:delete R["last-event-id"]},C=>{W=d=C},o))),s?.(),k(),y()}catch(E){if(!T.signal.aborted)try{let A=a?.(E)||d;window.clearTimeout(P),P=window.setTimeout(M,A),d=Math.min(d*f,u),++D>=p?(J(Kt,t,{}),k(),b("Max retries reached.")):console.error(`Datastar failed to reach ${e.toString()} retrying in ${A}ms.`)}catch(A){k(),b(A)}}}M()})}var cn=$("delete","DELETE");var ln=$("get","GET");var un=$("patch","PATCH");var fn=$("post","POST");var dn=$("put","PUT");var hn={type:"watcher",name:ce,async onGlobalInit(e){$e(ce,t=>jn(e,t))}};function jn(e,{elements:t,selector:n,mode:i=ut,useViewTransition:r}){if(i===_e&&n){let o=document.querySelectorAll(n);if(!o.length)throw K("NoTargetsFound",e,{selectorOrId:n});if(r&&Ie)document.startViewTransition(()=>{for(let s of o)s.remove()});else for(let s of o)s.remove()}else{let o=document.createElement("template");o.innerHTML=t;for(let s of[...o.content.childNodes]){let a=s.nodeType;if(a!==1){if(a===3&&!s.nodeValue.trim())continue;throw K("NoElementsFound",e)}let c=n||`#${s.id}`,l=document.querySelectorAll(c);if(!l.length)throw K("NoTargetsFound",e,{selectorOrId:c});r&&Ie?document.startViewTransition(()=>gn(e,i,s,l)):gn(e,i,s,l)}}}var pn=new WeakSet;function mn(e){let t=e instanceof HTMLScriptElement?[e]:e.querySelectorAll("script");for(let n of t)if(!pn.has(n)){let i=document.createElement("script");for(let{name:r,value:o}of n.attributes)i.setAttribute(r,o);i.text=n.text,n.replaceWith(i),pn.add(i)}}function gn(e,t,n,i){for(let r of i)if(t===_e)r.remove();else if(t===Ne||t===rt)Gn(r,n,t),mn(r);else{let o=n.cloneNode(!0);if(t===st)r.replaceWith(o);else if(t===ot)r.prepend(o);else if(t===at)r.append(o);else if(t===ct)r.before(o);else if(t===lt)r.after(o);else throw K("InvalidPatchMode",e,{mode:t});mn(o)}}var ie=new Map,O=new Map,re=new Set,ye=new Set,se=document.createElement("div");se.hidden=!0;function Gn(e,t,n){let i=w("ignore-morph");if(e.hasAttribute(i)&&t.hasAttribute(i)||e.parentElement?.closest(`[${i}]`))return;let r=document.createElement("div");r.append(t),document.body.insertAdjacentElement("afterend",se);let o=e.querySelectorAll("[id]");for(let{id:a,tagName:c}of o)ie.has(a)?ye.add(a):ie.set(a,c);e.id&&(ie.has(e.id)?ye.add(e.id):ie.set(e.id,e.tagName)),re.clear();let s=r.querySelectorAll("[id]");for(let{id:a,tagName:c}of s)re.has(a)?ye.add(a):ie.get(a)===c&&re.add(a);ie.clear();for(let a of ye)re.delete(a);ye.clear(),O.clear(),yn(n==="outer"?e.parentElement:e,o),yn(r,s),bn(n==="outer"?e.parentElement:e,r,n==="outer"?e:null,e.nextSibling),se.remove()}function bn(e,t,n=null,i=null){e instanceof HTMLTemplateElement&&t instanceof HTMLTemplateElement&&(e=e.content,t=t.content),n??=e.firstChild;for(let r of t.childNodes){if(n&&n!==i){let s=Wn(r,n,i);if(s){if(s!==n){let a=n;for(;a&&a!==s;){let c=a;a=a.nextSibling,Xe(c)}}Ze(s,r),n=s.nextSibling;continue}}let o=r.id;if(r instanceof Element&&re.has(o)){let s=window[o],a=s;for(;a=a.parentNode;){let c=O.get(a);c&&(c.delete(o),c.size||O.delete(a))}En(e,s,n),Ze(s,r),n=s.nextSibling;continue}if(O.has(r)){let s=document.createElement(r.tagName);e.insertBefore(s,n),Ze(s,r),n=s.nextSibling}else{let s=document.importNode(r,!0);e.insertBefore(s,n),n=s.nextSibling}}for(;n&&n!==i;){let r=n;n=n.nextSibling,Xe(r)}}function Wn(e,t,n){let i=null,r=e.nextSibling,o=0,s=0,a=O.get(e)?.size||0,c=t;for(;c&&c!==n;){if(vn(c,e)){let l=!1,d=O.get(c),f=O.get(e);if(f&&d){for(let u of d)if(f.has(u)){l=!0;break}}if(l)return c;if(!i&&!O.has(c)){if(!a)return c;i=c}}if(s+=O.get(c)?.size||0,s>a||(i===null&&r&&vn(c,r)&&(o++,r=r.nextSibling,o>=2&&(i=void 0)),c.contains(document.activeElement)))break;c=c.nextSibling}return i||null}function vn(e,t){let n=e.id;return e.nodeType===t.nodeType&&e.tagName===t.tagName&&(!n||n===t.id)}function Xe(e){O.has(e)?En(se,e,null):e.parentNode?.removeChild(e)}var En=Xe.call.bind(se.moveBefore??se.insertBefore);function Ze(e,t){let n=t.nodeType;if(n===1){let i=w("ignore-morph");if(e.hasAttribute(i)&&t.hasAttribute(i))return e;let r=(t.getAttribute(w("preserve-attr"))??"").split(" ");for(let{name:s,value:a}of t.attributes)e.getAttribute(s)!==a&&!r.includes(x(s))&&e.setAttribute(s,a);let o=e.attributes;for(let s=o.length-1;s>=0;s--){let{name:a}=o[s];!t.hasAttribute(a)&&!r.includes(x(a))&&e.removeAttribute(a)}if(e instanceof HTMLInputElement&&t instanceof HTMLInputElement&&t.type!=="file"){let s=w("bind").slice(5),a=!0;for(let c in t.dataset)if(c.startsWith(s)){a=!1;break}if(a){let c=t.value;t.hasAttribute("value")?e.value!==c&&(e.setAttribute("value",c),e.value=c):(e.value="",e.removeAttribute("value"))}}else if(e instanceof HTMLTextAreaElement&&t instanceof HTMLTextAreaElement){let s=t.value;s!==e.value&&(e.value=s),e.firstChild&&e.firstChild.nodeValue!==s&&(e.firstChild.nodeValue=s)}}return(n===8||n===3)&&e.nodeValue!==t.nodeValue&&(e.nodeValue=t.nodeValue),e.isEqualNode(t)||bn(e,t),e}function yn(e,t){for(let n of t)if(re.has(n.id)){let i=n;for(;i&&i!==e;){let r=O.get(i);r||(r=new Set,O.set(i,r)),r.add(n.id),i=i.parentElement}}}var Sn={type:"watcher",name:le,onGlobalInit:e=>$e(le,({signals:t="{}",onlyIfMissing:n=`${it}`})=>e.mergePatch(ee(t),{ifMissing:ft(n)}))};Je(ln,fn,dn,un,cn,hn,Sn,Vt,Nt,_t,qt,jt,Ut,Bt,Jt,zt,Qt,Yt,Zt,Xt,nn,rn,sn,$t,It,Ht);ze();export{ze as apply,Je as load,kt as setAlias};
//# sourceMappingURL=datastar.js.map

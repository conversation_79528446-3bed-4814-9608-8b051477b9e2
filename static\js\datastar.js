// Datastar v1.0.0-beta.11
var et=/🖕JS_DS🚀/.source,_e=et.slice(0,5),le=et.slice(4),q="datastar",tt="Datastar-Request",nt=1e3,rt="type module",Re=!1,it=!1,ot=!0,U={Morph:"morph",Inner:"inner",Outer:"outer",Prepend:"prepend",Append:"append",Before:"before",After:"after",UpsertAttributes:"upsertAttributes"},st=U.Morph,F={MergeFragments:"datastar-merge-fragments",MergeSignals:"datastar-merge-signals",RemoveFragments:"datastar-remove-fragments",RemoveSignals:"datastar-remove-signals",ExecuteScript:"datastar-execute-script"};var x=(r=>(r[r.Attribute=1]="Attribute",r[r.Watcher=2]="Watcher",r[r.Action=3]="Action",r))(x||{});var ye=`${q}-signals`;var X=t=>t.trim()==="true",Y=t=>t.replace(/[A-Z]+(?![a-z])|[A-Z]/g,(e,n)=>(n?"-":"")+e.toLowerCase()),ve=t=>Y(t).replace(/-./g,e=>e[1].toUpperCase()),qe=t=>Y(t).replace(/-/g,"_"),Sn=t=>ve(t).replace(/^./,e=>e[0].toUpperCase()),xe=t=>new Function(`return Object.assign({}, ${t})`)(),Z=t=>t.startsWith("$")?t.slice(1):t,En={kebab:Y,snake:qe,pascal:Sn};function H(t,e){for(let n of e.get("case")||[]){let r=En[n];r&&(t=r(t))}return t}var Tn="computed",at={type:1,name:Tn,keyReq:1,valReq:1,onLoad:({key:t,mods:e,signals:n,genRX:r})=>{t=H(t,e);let i=r();n.setComputed(t,i)}};var lt={type:1,name:"signals",onLoad:t=>{let{key:e,mods:n,signals:r,value:i,genRX:o}=t,s=n.has("ifmissing");if(e!==""){let a=H(e,n),p=i===""?i:o()();s?r.upsertIfMissing(a,p):r.setValue(a,p)}else{let a=xe(t.value);t.value=JSON.stringify(a);let y=o()();r.merge(y,s)}}};var ut={type:1,name:"star",keyReq:2,valReq:2,onLoad:()=>{alert("YOU ARE PROBABLY OVERCOMPLICATING IT")}};var ue=class{#e=0;#t;constructor(e=q){this.#t=e}with(e){if(typeof e=="string")for(let n of e.split(""))this.with(n.charCodeAt(0));else typeof e=="boolean"?this.with(1<<(e?7:3)):this.#e=this.#e*33^e;return this}get value(){return this.#e}get string(){return this.#t+Math.abs(this.#e).toString(36)}};function we(t){if(t.id)return t.id;let e=new ue,n=t;for(;n;){if(e.with(n.tagName||""),n.id){e.with(n.id);break}let r=n?.parentNode;r&&e.with([...r.children].indexOf(n)),n=r}return e.string}function Me(t,e){return new ue().with(t).with(e).value}function be(t,e){if(!t||!(t instanceof HTMLElement||t instanceof SVGElement))return null;let n=t.dataset;if("starIgnore"in n)return null;"starIgnore__self"in n||e(t);let r=t.firstElementChild;for(;r;)be(r,e),r=r.nextElementSibling}var An="https://data-star.dev/errors";function $e(t,e,n={}){let r=new Error;r.name=`${q} ${t} error`;let i=qe(e),o=new URLSearchParams({metadata:JSON.stringify(n)}).toString(),s=JSON.stringify(n,null,2);return r.message=`${e}
More info: ${An}/${t}/${i}?${o}
Context: ${s}`,r}function j(t,e,n={}){return $e("internal",e,Object.assign({from:t},n))}function $(t,e,n={}){let r={plugin:{name:e.plugin.name,type:x[e.plugin.type]}};return $e("init",t,Object.assign(r,n))}function P(t,e,n={}){let r={plugin:{name:e.plugin.name,type:x[e.plugin.type]},element:{id:e.el.id,tag:e.el.tagName},expression:{rawKey:e.rawKey,key:e.key,value:e.value,validSignals:e.signals.paths(),fnContent:e.fnContent}};return $e("runtime",t,Object.assign(r,n))}var ce="preact-signals",_n=Symbol.for("preact-signals"),K=1,fe=2,Ee=4,pe=8,Pe=16,de=32;function Be(){Ce++}function Ge(){if(Ce>1){Ce--;return}let t,e=!1;for(;Se!==void 0;){let n=Se;for(Se=void 0,We++;n!==void 0;){let r=n._nextBatchedEffect;if(n._nextBatchedEffect=void 0,n._flags&=~fe,!(n._flags&pe)&&ft(n))try{n._callback()}catch(i){e||(t=i,e=!0)}n=r}}if(We=0,Ce--,e)throw t}var C;var Se,Ce=0,We=0,Ne=0;function ct(t){if(C===void 0)return;let e=t._node;if(e===void 0||e._target!==C)return e={_version:0,_source:t,_prevSource:C._sources,_nextSource:void 0,_target:C,_prevTarget:void 0,_nextTarget:void 0,_rollbackNode:e},C._sources!==void 0&&(C._sources._nextSource=e),C._sources=e,t._node=e,C._flags&de&&t._subscribe(e),e;if(e._version===-1)return e._version=0,e._nextSource!==void 0&&(e._nextSource._prevSource=e._prevSource,e._prevSource!==void 0&&(e._prevSource._nextSource=e._nextSource),e._prevSource=C._sources,e._nextSource=void 0,C._sources._nextSource=e,C._sources=e),e}function I(t){this._value=t,this._version=0,this._node=void 0,this._targets=void 0}I.prototype.brand=_n;I.prototype._refresh=()=>!0;I.prototype._subscribe=function(t){this._targets!==t&&t._prevTarget===void 0&&(t._nextTarget=this._targets,this._targets!==void 0&&(this._targets._prevTarget=t),this._targets=t)};I.prototype._unsubscribe=function(t){if(this._targets!==void 0){let e=t._prevTarget,n=t._nextTarget;e!==void 0&&(e._nextTarget=n,t._prevTarget=void 0),n!==void 0&&(n._prevTarget=e,t._nextTarget=void 0),t===this._targets&&(this._targets=n)}};I.prototype.subscribe=function(t){return me(()=>{let e=this.value,n=C;C=void 0;try{t(e)}finally{C=n}})};I.prototype.valueOf=function(){return this.value};I.prototype.toString=function(){return`${this.value}`};I.prototype.toJSON=function(){return this.value};I.prototype.peek=function(){let t=C;C=void 0;try{return this.value}finally{C=t}};Object.defineProperty(I.prototype,"value",{get(){let t=ct(this);return t!==void 0&&(t._version=this._version),this._value},set(t){if(t!==this._value){if(We>100)throw j(ce,"SignalCycleDetected");let e=this._value,n=t;this._value=t,this._version++,Ne++,Be();try{for(let r=this._targets;r!==void 0;r=r._nextTarget)r._target._notify()}finally{Ge()}this?._onChange({old:e,revised:n})}}});function ft(t){for(let e=t._sources;e!==void 0;e=e._nextSource)if(e._source._version!==e._version||!e._source._refresh()||e._source._version!==e._version)return!0;return!1}function dt(t){for(let e=t._sources;e!==void 0;e=e._nextSource){let n=e._source._node;if(n!==void 0&&(e._rollbackNode=n),e._source._node=e,e._version=-1,e._nextSource===void 0){t._sources=e;break}}}function pt(t){let e=t._sources,n;for(;e!==void 0;){let r=e._prevSource;e._version===-1?(e._source._unsubscribe(e),r!==void 0&&(r._nextSource=e._nextSource),e._nextSource!==void 0&&(e._nextSource._prevSource=r)):n=e,e._source._node=e._rollbackNode,e._rollbackNode!==void 0&&(e._rollbackNode=void 0),e=r}t._sources=n}function ne(t){I.call(this,void 0),this._fn=t,this._sources=void 0,this._globalVersion=Ne-1,this._flags=Ee}ne.prototype=new I;ne.prototype._refresh=function(){if(this._flags&=~fe,this._flags&K)return!1;if((this._flags&(Ee|de))===de||(this._flags&=~Ee,this._globalVersion===Ne))return!0;if(this._globalVersion=Ne,this._flags|=K,this._version>0&&!ft(this))return this._flags&=~K,!0;let t=C;try{dt(this),C=this;let e=this._fn();(this._flags&Pe||this._value!==e||this._version===0)&&(this._value=e,this._flags&=~Pe,this._version++)}catch(e){this._value=e,this._flags|=Pe,this._version++}return C=t,pt(this),this._flags&=~K,!0};ne.prototype._subscribe=function(t){if(this._targets===void 0){this._flags|=Ee|de;for(let e=this._sources;e!==void 0;e=e._nextSource)e._source._subscribe(e)}I.prototype._subscribe.call(this,t)};ne.prototype._unsubscribe=function(t){if(this._targets!==void 0&&(I.prototype._unsubscribe.call(this,t),this._targets===void 0)){this._flags&=~de;for(let e=this._sources;e!==void 0;e=e._nextSource)e._source._unsubscribe(e)}};ne.prototype._notify=function(){if(!(this._flags&fe)){this._flags|=Ee|fe;for(let t=this._targets;t!==void 0;t=t._nextTarget)t._target._notify()}};Object.defineProperty(ne.prototype,"value",{get(){if(this._flags&K)throw j(ce,"SignalCycleDetected");let t=ct(this);if(this._refresh(),t!==void 0&&(t._version=this._version),this._flags&Pe)throw j(ce,"GetComputedError",{value:this._value});return this._value}});function mt(t){return new ne(t)}function gt(t){let e=t._cleanup;if(t._cleanup=void 0,typeof e=="function"){Be();let n=C;C=void 0;try{e()}catch(r){throw t._flags&=~K,t._flags|=pe,Ue(t),j(ce,"CleanupEffectError",{error:r})}finally{C=n,Ge()}}}function Ue(t){for(let e=t._sources;e!==void 0;e=e._nextSource)e._source._unsubscribe(e);t._fn=void 0,t._sources=void 0,gt(t)}function Rn(t){if(C!==this)throw j(ce,"EndEffectError");pt(this),C=t,this._flags&=~K,this._flags&pe&&Ue(this),Ge()}function Te(t){this._fn=t,this._cleanup=void 0,this._sources=void 0,this._nextBatchedEffect=void 0,this._flags=de}Te.prototype._callback=function(){let t=this._start();try{if(this._flags&pe||this._fn===void 0)return;let e=this._fn();typeof e=="function"&&(this._cleanup=e)}finally{t()}};Te.prototype._start=function(){if(this._flags&K)throw j(ce,"SignalCycleDetected");this._flags|=K,this._flags&=~pe,gt(this),dt(this),Be();let t=C;return C=this,Rn.bind(this,t)};Te.prototype._notify=function(){this._flags&fe||(this._flags|=fe,this._nextBatchedEffect=Se,Se=this)};Te.prototype._dispose=function(){this._flags|=pe,this._flags&K||Ue(this)};function me(t){let e=new Te(t);try{e._callback()}catch(n){throw e._dispose(),n}return e._dispose.bind(e)}var ht="namespacedSignals",ge=t=>{document.dispatchEvent(new CustomEvent(ye,{detail:Object.assign({added:[],removed:[],updated:[]},t)}))};function yt(t,e=!1){let n={};for(let r in t)if(Object.hasOwn(t,r)){if(e&&r.startsWith("_"))continue;let i=t[r];i instanceof I?n[r]=i.value:n[r]=yt(i)}return n}function vt(t,e,n=!1){let r={added:[],removed:[],updated:[]};for(let i in e)if(Object.hasOwn(e,i)){if(i.match(/\_\_+/))throw j(ht,"InvalidSignalKey",{key:i});let o=e[i];if(o instanceof Object&&!Array.isArray(o)){t[i]||(t[i]={});let s=vt(t[i],o,n);r.added.push(...s.added.map(a=>`${i}.${a}`)),r.removed.push(...s.removed.map(a=>`${i}.${a}`)),r.updated.push(...s.updated.map(a=>`${i}.${a}`))}else{if(Object.hasOwn(t,i)){if(n)continue;let p=t[i];if(p instanceof I){let y=p.value;p.value=o,y!==o&&r.updated.push(i);continue}}let a=new I(o);a._onChange=()=>{ge({updated:[i]})},t[i]=a,r.added.push(i)}}return r}function bt(t,e){for(let n in t)if(Object.hasOwn(t,n)){let r=t[n];r instanceof I?e(n,r):bt(r,(i,o)=>{e(`${n}.${i}`,o)})}}function xn(t,...e){let n={};for(let r of e){let i=r.split("."),o=t,s=n;for(let p=0;p<i.length-1;p++){let y=i[p];if(!o[y])return{};s[y]||(s[y]={}),o=o[y],s=s[y]}let a=i[i.length-1];s[a]=o[a]}return n}var ke=class{#e={};exists(e){return!!this.signal(e)}signal(e){let n=e.split("."),r=this.#e;for(let s=0;s<n.length-1;s++){let a=n[s];if(!r[a])return null;r=r[a]}let i=n[n.length-1],o=r[i];if(!o)throw j(ht,"SignalNotFound",{path:e});return o}setSignal(e,n){let r=e.split("."),i=this.#e;for(let s=0;s<r.length-1;s++){let a=r[s];i[a]||(i[a]={}),i=i[a]}let o=r[r.length-1];i[o]=n}setComputed(e,n){let r=mt(()=>n());this.setSignal(e,r)}value(e){return this.signal(e)?.value}setValue(e,n){let{signal:r}=this.upsertIfMissing(e,n),i=r.value;r.value=n,i!==n&&ge({updated:[e]})}upsertIfMissing(e,n){let r=e.split("."),i=this.#e;for(let p=0;p<r.length-1;p++){let y=r[p];i[y]||(i[y]={}),i=i[y]}let o=r[r.length-1],s=i[o];if(s instanceof I)return{signal:s,inserted:!1};let a=new I(n);return a._onChange=()=>{ge({updated:[e]})},i[o]=a,ge({added:[e]}),{signal:a,inserted:!0}}remove(...e){if(!e.length){this.#e={};return}let n=Array();for(let r of e){let i=r.split("."),o=this.#e;for(let a=0;a<i.length-1;a++){let p=i[a];if(!o[p])return;o=o[p]}let s=i[i.length-1];delete o[s],n.push(r)}ge({removed:n})}merge(e,n=!1){let r=vt(this.#e,e,n);(r.added.length||r.removed.length||r.updated.length)&&ge(r)}subset(...e){return xn(this.values(),...e)}walk(e){bt(this.#e,e)}paths(){let e=new Array;return this.walk(n=>e.push(n)),e}values(e=!1){return yt(this.#e,e)}JSON(e=!0,n=!1){let r=this.values(n);return e?JSON.stringify(r,null,2):JSON.stringify(r)}toString(){return this.JSON()}};var St=new ke,Ie={},Ke=[],re=new Map,je=null,Je="";function Et(t){Je=t}function Ve(...t){for(let e of t){let n={plugin:e,signals:St,effect:i=>me(i),actions:Ie,removals:re,applyToElement:Le},r;switch(e.type){case 3:{Ie[e.name]=e;break}case 1:{let i=e;Ke.push(i),r=i.onGlobalInit;break}case 2:{r=e.onGlobalInit;break}default:throw $("InvalidPluginType",n)}r&&r(n)}Ke.sort((e,n)=>{let r=n.name.length-e.name.length;return r!==0?r:e.name.localeCompare(n.name)})}function ze(){queueMicrotask(()=>{Le(document.documentElement),wn()})}function Le(t){be(t,e=>{let n=new Array,r=re.get(e.id)||new Map,i=new Map([...r]),o=new Map;for(let s of Object.keys(e.dataset)){if(!s.startsWith(Je))break;let a=e.dataset[s]||"",p=Me(s,a);o.set(s,p),r.has(p)?i.delete(p):n.push(s)}for(let[s,a]of i)a();for(let s of n){let a=o.get(s);Mn(e,s,a)}})}function wn(){je||(je=new MutationObserver(t=>{let e=new Set,n=new Set;for(let{target:r,type:i,addedNodes:o,removedNodes:s}of t)switch(i){case"childList":{for(let a of s)e.add(a);for(let a of o)n.add(a)}break;case"attributes":{n.add(r);break}}for(let r of e){let i=re.get(r.id);if(i){for(let[o,s]of i)s(),i.delete(o);i.size===0&&re.delete(r.id)}}for(let r of n)Le(r)}),je.observe(document.body,{attributes:!0,attributeOldValue:!0,childList:!0,subtree:!0}))}function Mn(t,e,n){let r=ve(e.slice(Je.length)),i=Ke.find(T=>new RegExp(`^${T.name}([A-Z]|_|$)`).test(r));if(!i)return;t.id.length||(t.id=we(t));let[o,...s]=r.slice(i.name.length).split(/\_\_+/),a=o.length>0;a&&(o=ve(o));let p=t.dataset[e]||"",y=p.length>0,v={signals:St,applyToElement:Le,effect:T=>me(T),actions:Ie,removals:re,genRX:()=>Pn(v,...i.argNames||[]),plugin:i,el:t,rawKey:r,key:o,value:p,mods:new Map},k=i.keyReq||0;if(a){if(k===2)throw P(`${i.name}KeyNotAllowed`,v)}else if(k===1)throw P(`${i.name}KeyRequired`,v);let b=i.valReq||0;if(y){if(b===2)throw P(`${i.name}ValueNotAllowed`,v)}else if(b===1)throw P(`${i.name}ValueRequired`,v);if(k===3||b===3){if(a&&y)throw P(`${i.name}KeyAndValueProvided`,v);if(!a&&!y)throw P(`${i.name}KeyOrValueRequired`,v)}for(let T of s){let[_,...E]=T.split(".");v.mods.set(ve(_),new Set(E.map(c=>c.toLowerCase())))}let A=i.onLoad(v)??(()=>{}),h=re.get(t.id);h||(h=new Map,re.set(t.id,h)),h.set(n,A)}function Pn(t,...e){let n="",r=/(\/(\\\/|[^\/])*\/|"(\\"|[^\"])*"|'(\\'|[^'])*'|`(\\`|[^`])*`|[^;])+/gm,i=t.value.trim().match(r);if(i){let A=i.length-1,h=i[A].trim();h.startsWith("return")||(i[A]=`return (${h});`),n=i.join(`;
`)}let o=new Map,s=new RegExp(`(?:${_e})(.*?)(?:${le})`,"gm");for(let A of n.matchAll(s)){let h=A[1],T=new ue("dsEscaped").with(h).string;o.set(T,h),n=n.replace(_e+h+le,T)}let a=/@(\w*)\(/gm,p=n.matchAll(a),y=new Set;for(let A of p)y.add(A[1]);let v=new RegExp(`@(${Object.keys(Ie).join("|")})\\(`,"gm");n=n.replaceAll(v,"ctx.actions.$1.fn(ctx,");let k=t.signals.paths();if(k.length){let A=new RegExp(`\\$(${k.join("|")})(\\W|$)`,"gm");n=n.replaceAll(A,"ctx.signals.signal('$1').value$2")}for(let[A,h]of o)n=n.replace(A,h);let b=`return (() => {
${n}
})()`;t.fnContent=b;try{let A=new Function("ctx",...e,b);return(...h)=>{try{return A(t,...h)}catch(T){throw P("ExecuteExpression",t,{error:T.message})}}}catch(A){throw P("GenerateExpression",t,{error:A.message})}}Ve(ut,lt,at);var ie=`${q}-sse`,De="started",Oe="finished",Tt="error",At="retrying",_t="retries-failed";function J(t,e){document.addEventListener(ie,n=>{if(n.detail.type!==t)return;let{argsRaw:r}=n.detail;e(r)})}function Q(t,e,n){document.dispatchEvent(new CustomEvent(ie,{detail:{type:t,elId:e,argsRaw:n}}))}async function Cn(t,e){let n=t.getReader(),r;for(;!(r=await n.read()).done;)e(r.value)}function Nn(t){let e,n,r,i=!1;return function(s){e===void 0?(e=s,n=0,r=-1):e=In(e,s);let a=e.length,p=0;for(;n<a;){i&&(e[n]===10&&(p=++n),i=!1);let y=-1;for(;n<a&&y===-1;++n)switch(e[n]){case 58:r===-1&&(r=n-p);break;case 13:i=!0;case 10:y=n;break}if(y===-1)break;t(e.subarray(p,y),r),p=n,r=-1}p===a?e=void 0:p!==0&&(e=e.subarray(p),n-=p)}}function kn(t,e,n){let r=Rt(),i=new TextDecoder;return function(s,a){if(s.length===0)n?.(r),r=Rt();else if(a>0){let p=i.decode(s.subarray(0,a)),y=a+(s[a+1]===32?2:1),v=i.decode(s.subarray(y));switch(p){case"data":r.data=r.data?`${r.data}
${v}`:v;break;case"event":r.event=v;break;case"id":t(r.id=v);break;case"retry":{let k=Number.parseInt(v,10);Number.isNaN(k)||e(r.retry=k);break}}}}}function In(t,e){let n=new Uint8Array(t.length+e.length);return n.set(t),n.set(e,t.length),n}function Rt(){return{data:"",event:"",id:"",retry:void 0}}var Vn="text/event-stream",xt="last-event-id";function wt(t,e,{signal:n,headers:r,onopen:i,onmessage:o,onclose:s,onerror:a,openWhenHidden:p,fetch:y,retryInterval:v=1e3,retryScaler:k=2,retryMaxWaitMs:b=3e4,retryMaxCount:A=10,...h}){return new Promise((T,_)=>{let E=0,c={...r};c.accept||(c.accept=Vn);let d;function u(){d.abort(),document.hidden||S()}p||document.addEventListener("visibilitychange",u);let l=0;function g(){document.removeEventListener("visibilitychange",u),window.clearTimeout(l),d.abort()}n?.addEventListener("abort",()=>{g(),T()});let m=y??window.fetch,f=i??function(){};async function S(){d=new AbortController;try{let w=await m(t,{...h,headers:c,signal:d.signal});await f(w),await Cn(w.body,Nn(kn(R=>{R?c[xt]=R:delete c[xt]},R=>{v=R},o))),s?.(),g(),T()}catch(w){if(!d.signal.aborted)try{let R=a?.(w)??v;window.clearTimeout(l),l=window.setTimeout(S,R),v*=k,v=Math.min(v,b),E++,E>A?(Q(_t,e,{}),g(),_("Max retries reached.")):console.error(`Datastar failed to reach ${t.toString()} retrying in ${R}ms.`)}catch(R){g(),_(R)}}}S()})}var Mt=t=>`${t}`.includes("text/event-stream"),z=async(t,e,n,r)=>{let{el:i,signals:o}=t,s=i.id,{headers:a,contentType:p,includeLocal:y,selector:v,openWhenHidden:k,retryInterval:b,retryScaler:A,retryMaxWaitMs:h,retryMaxCount:T,abort:_}=Object.assign({headers:{},contentType:"json",includeLocal:!1,selector:null,openWhenHidden:!1,retryInterval:nt,retryScaler:2,retryMaxWaitMs:3e4,retryMaxCount:10,abort:void 0},r),E=e.toLowerCase(),c=()=>{};try{if(Q(De,s,{}),!n?.length)throw P("SseNoUrlProvided",t,{action:E});let d={};d[tt]=!0,p==="json"&&(d["Content-Type"]="application/json");let u=Object.assign({},d,a),l={method:e,headers:u,openWhenHidden:k,retryInterval:b,retryScaler:A,retryMaxWaitMs:h,retryMaxCount:T,signal:_,onopen:async f=>{if(f.status>=400){let S=f.status.toString();Q(Tt,s,{status:S})}},onmessage:f=>{if(!f.event.startsWith(q))return;let S=f.event,w={},R=f.data.split(`
`);for(let L of R){let N=L.indexOf(" "),O=L.slice(0,N),D=w[O];D||(D=[],w[O]=D);let B=L.slice(N+1);D.push(B)}let M={};for(let[L,N]of Object.entries(w))M[L]=N.join(`
`);Q(S,s,M)},onerror:f=>{if(Mt(f))throw P("InvalidContentType",t,{url:n});f&&(console.error(f.message),Q(At,s,{message:f.message}))}},g=new URL(n,window.location.origin),m=new URLSearchParams(g.search);if(p==="json"){let f=o.JSON(!1,!y);e==="GET"?m.set(q,f):l.body=f}else if(p==="form"){let f=v?document.querySelector(v):i.closest("form");if(f===null)throw v?P("SseFormNotFound",t,{action:E,selector:v}):P("SseClosestFormNotFound",t,{action:E});if(i!==f){let w=R=>R.preventDefault();f.addEventListener("submit",w),c=()=>f.removeEventListener("submit",w)}if(!f.checkValidity()){f.reportValidity(),c();return}let S=new FormData(f);if(e==="GET"){let w=new URLSearchParams(S);for(let[R,M]of w)m.set(R,M)}else l.body=S}else throw P("SseInvalidContentType",t,{action:E,contentType:p});g.search=m.toString();try{await wt(g.toString(),s,l)}catch(f){if(!Mt(f))throw P("SseFetchFailed",t,{method:e,url:n,error:f})}}finally{Q(Oe,s,{}),c()}};var Pt={type:3,name:"delete",fn:async(t,e,n)=>z(t,"DELETE",e,{...n})};var Ct={type:3,name:"get",fn:async(t,e,n)=>z(t,"GET",e,{...n})};var Nt={type:3,name:"patch",fn:async(t,e,n)=>z(t,"PATCH",e,{...n})};var kt={type:3,name:"post",fn:async(t,e,n)=>z(t,"POST",e,{...n})};var It={type:3,name:"put",fn:async(t,e,n)=>z(t,"PUT",e,{...n})};var Vt={type:1,name:"indicator",keyReq:3,valReq:3,onLoad:({el:t,key:e,mods:n,signals:r,value:i})=>{let o=e?H(e,n):Z(i),{signal:s}=r.upsertIfMissing(o,!1),a=p=>{let{type:y,elId:v}=p.detail;if(v===t.id)switch(y){case De:s.value=!0;break;case Oe:s.value=!1,document.removeEventListener(ie,a);break}};document.addEventListener(ie,a)}};var Lt={type:2,name:F.ExecuteScript,onGlobalInit:async t=>{J(F.ExecuteScript,({autoRemove:e=`${ot}`,attributes:n=rt,script:r})=>{let i=X(e);if(!r?.length)throw $("NoScriptProvided",t);let o=document.createElement("script");for(let s of n.split(`
`)){let a=s.indexOf(" "),p=a?s.slice(0,a):s,y=a?s.slice(a):"";o.setAttribute(p.trim(),y.trim())}o.text=r,document.head.appendChild(o),i&&o.remove()})}};var Ae=document,oe=!!Ae.startViewTransition;function W(t,e){if(e.has("viewtransition")&&oe){let n=t;t=(...r)=>document.startViewTransition(()=>n(...r))}return t}var Dt=function(){"use strict";let t=()=>{},e={morphStyle:"outerHTML",callbacks:{beforeNodeAdded:t,afterNodeAdded:t,beforeNodeMorphed:t,afterNodeMorphed:t,beforeNodeRemoved:t,afterNodeRemoved:t,beforeAttributeUpdated:t},head:{style:"merge",shouldPreserve:b=>b.getAttribute("im-preserve")==="true",shouldReAppend:b=>b.getAttribute("im-re-append")==="true",shouldRemove:t,afterHeadMorphed:t},restoreFocus:!0};function n(b,A,h={}){b=v(b);let T=k(A),_=y(b,T,h),E=i(_,()=>a(_,b,T,c=>c.morphStyle==="innerHTML"?(o(c,b,T),Array.from(b.childNodes)):r(c,b,T)));return _.pantry.remove(),E}function r(b,A,h){let T=k(A);return o(b,T,h,A,A.nextSibling),Array.from(T.childNodes)}function i(b,A){if(!b.config.restoreFocus)return A();let h=document.activeElement;if(!(h instanceof HTMLInputElement||h instanceof HTMLTextAreaElement))return A();let{id:T,selectionStart:_,selectionEnd:E}=h,c=A();return T&&T!==document.activeElement?.id&&(h=b.target.querySelector(`[id="${T}"]`),h?.focus()),h&&!h.selectionEnd&&E&&h.setSelectionRange(_,E),c}let o=function(){function b(u,l,g,m=null,f=null){l instanceof HTMLTemplateElement&&g instanceof HTMLTemplateElement&&(l=l.content,g=g.content),m||=l.firstChild;for(let S of g.childNodes){if(m&&m!=f){let R=h(u,S,m,f);if(R){R!==m&&_(u,m,R),s(R,S,u),m=R.nextSibling;continue}}if(S instanceof Element&&u.persistentIds.has(S.id)){let R=E(l,S.id,m,u);s(R,S,u),m=R.nextSibling;continue}let w=A(l,S,m,u);w&&(m=w.nextSibling)}for(;m&&m!=f;){let S=m;m=m.nextSibling,T(u,S)}}function A(u,l,g,m){if(m.callbacks.beforeNodeAdded(l)===!1)return null;if(m.idMap.has(l)){let f=document.createElement(l.tagName);return u.insertBefore(f,g),s(f,l,m),m.callbacks.afterNodeAdded(f),f}else{let f=document.importNode(l,!0);return u.insertBefore(f,g),m.callbacks.afterNodeAdded(f),f}}let h=function(){function u(m,f,S,w){let R=null,M=f.nextSibling,L=0,N=S;for(;N&&N!=w;){if(g(N,f)){if(l(m,N,f))return N;R===null&&(m.idMap.has(N)||(R=N))}if(R===null&&M&&g(N,M)&&(L++,M=M.nextSibling,L>=2&&(R=void 0)),N.contains(document.activeElement))break;N=N.nextSibling}return R||null}function l(m,f,S){let w=m.idMap.get(f),R=m.idMap.get(S);if(!R||!w)return!1;for(let M of w)if(R.has(M))return!0;return!1}function g(m,f){let S=m,w=f;return S.nodeType===w.nodeType&&S.tagName===w.tagName&&(!S.id||S.id===w.id)}return u}();function T(u,l){if(u.idMap.has(l))d(u.pantry,l,null);else{if(u.callbacks.beforeNodeRemoved(l)===!1)return;l.parentNode?.removeChild(l),u.callbacks.afterNodeRemoved(l)}}function _(u,l,g){let m=l;for(;m&&m!==g;){let f=m;m=m.nextSibling,T(u,f)}return m}function E(u,l,g,m){let f=m.target.id===l&&m.target||m.target.querySelector(`[id="${l}"]`)||m.pantry.querySelector(`[id="${l}"]`);return c(f,m),d(u,f,g),f}function c(u,l){let g=u.id;for(;u=u.parentNode;){let m=l.idMap.get(u);m&&(m.delete(g),m.size||l.idMap.delete(u))}}function d(u,l,g){if(u.moveBefore)try{u.moveBefore(l,g)}catch{u.insertBefore(l,g)}else u.insertBefore(l,g)}return b}(),s=function(){function b(c,d,u){return u.ignoreActive&&c===document.activeElement?null:(u.callbacks.beforeNodeMorphed(c,d)===!1||(c instanceof HTMLHeadElement&&u.head.ignore||(c instanceof HTMLHeadElement&&u.head.style!=="morph"?p(c,d,u):(A(c,d,u),E(c,u)||o(u,c,d))),u.callbacks.afterNodeMorphed(c,d)),c)}function A(c,d,u){let l=d.nodeType;if(l===1){let g=c,m=d,f=g.attributes,S=m.attributes;for(let w of S)_(w.name,g,"update",u)||g.getAttribute(w.name)!==w.value&&g.setAttribute(w.name,w.value);for(let w=f.length-1;0<=w;w--){let R=f[w];if(R&&!m.hasAttribute(R.name)){if(_(R.name,g,"remove",u))continue;g.removeAttribute(R.name)}}E(g,u)||h(g,m,u)}(l===8||l===3)&&c.nodeValue!==d.nodeValue&&(c.nodeValue=d.nodeValue)}function h(c,d,u){if(c instanceof HTMLInputElement&&d instanceof HTMLInputElement&&d.type!=="file"){let l=d.value,g=c.value;T(c,d,"checked",u),T(c,d,"disabled",u),d.hasAttribute("value")?g!==l&&(_("value",c,"update",u)||(c.setAttribute("value",l),c.value=l)):_("value",c,"remove",u)||(c.value="",c.removeAttribute("value"))}else if(c instanceof HTMLOptionElement&&d instanceof HTMLOptionElement)T(c,d,"selected",u);else if(c instanceof HTMLTextAreaElement&&d instanceof HTMLTextAreaElement){let l=d.value,g=c.value;if(_("value",c,"update",u))return;l!==g&&(c.value=l),c.firstChild&&c.firstChild.nodeValue!==l&&(c.firstChild.nodeValue=l)}}function T(c,d,u,l){let g=d[u],m=c[u];if(g!==m){let f=_(u,c,"update",l);f||(c[u]=d[u]),g?f||c.setAttribute(u,""):_(u,c,"remove",l)||c.removeAttribute(u)}}function _(c,d,u,l){return c==="value"&&l.ignoreActiveValue&&d===document.activeElement?!0:l.callbacks.beforeAttributeUpdated(c,d,u)===!1}function E(c,d){return!!d.ignoreActiveValue&&c===document.activeElement&&c!==document.body}return b}();function a(b,A,h,T){if(b.head.block){let _=A.querySelector("head"),E=h.querySelector("head");if(_&&E){let c=p(_,E,b);return Promise.all(c).then(()=>{let d=Object.assign(b,{head:{block:!1,ignore:!0}});return T(d)})}}return T(b)}function p(b,A,h){let T=[],_=[],E=[],c=[],d=new Map;for(let l of A.children)d.set(l.outerHTML,l);for(let l of b.children){let g=d.has(l.outerHTML),m=h.head.shouldReAppend(l),f=h.head.shouldPreserve(l);g||f?m?_.push(l):(d.delete(l.outerHTML),E.push(l)):h.head.style==="append"?m&&(_.push(l),c.push(l)):h.head.shouldRemove(l)!==!1&&_.push(l)}c.push(...d.values());let u=[];for(let l of c){let g=document.createRange().createContextualFragment(l.outerHTML).firstChild;if(h.callbacks.beforeNodeAdded(g)!==!1){if("href"in g&&g.href||"src"in g&&g.src){let m,f=new Promise(function(S){m=S});g.addEventListener("load",function(){m()}),u.push(f)}b.appendChild(g),h.callbacks.afterNodeAdded(g),T.push(g)}}for(let l of _)h.callbacks.beforeNodeRemoved(l)!==!1&&(b.removeChild(l),h.callbacks.afterNodeRemoved(l));return h.head.afterHeadMorphed(b,{added:T,kept:E,removed:_}),u}let y=function(){function b(d,u,l){let{persistentIds:g,idMap:m}=E(d,u),f=A(l),S=f.morphStyle||"outerHTML";if(!["innerHTML","outerHTML"].includes(S))throw`Do not understand how to morph style ${S}`;return{target:d,newContent:u,config:f,morphStyle:S,ignoreActive:f.ignoreActive,ignoreActiveValue:f.ignoreActiveValue,restoreFocus:f.restoreFocus,idMap:m,persistentIds:g,pantry:h(),callbacks:f.callbacks,head:f.head}}function A(d){let u=Object.assign({},e);return Object.assign(u,d),u.callbacks=Object.assign({},e.callbacks,d.callbacks),u.head=Object.assign({},e.head,d.head),u}function h(){let d=document.createElement("div");return d.hidden=!0,document.body.insertAdjacentElement("afterend",d),d}function T(d){let u=Array.from(d.querySelectorAll("[id]"));return d.id&&u.push(d),u}function _(d,u,l,g){for(let m of g)if(u.has(m.id)){let f=m;for(;f;){let S=d.get(f);if(S==null&&(S=new Set,d.set(f,S)),S.add(m.id),f===l)break;f=f.parentElement}}}function E(d,u){let l=T(d),g=T(u),m=c(l,g),f=new Map;_(f,m,d,l);let S=u.__idiomorphRoot||u;return _(f,m,S,g),{persistentIds:m,idMap:f}}function c(d,u){let l=new Set,g=new Map;for(let{id:f,tagName:S}of d)g.has(f)?l.add(f):g.set(f,S);let m=new Set;for(let{id:f,tagName:S}of u)m.has(f)?l.add(f):g.get(f)===S&&m.add(f);for(let f of l)m.delete(f);return m}return b}(),{normalizeElement:v,normalizeParent:k}=function(){let b=new WeakSet;function A(E){return E instanceof Document?E.documentElement:E}function h(E){if(E==null)return document.createElement("div");if(typeof E=="string")return h(_(E));if(b.has(E))return E;if(E instanceof Node){if(E.parentNode)return new T(E);{let c=document.createElement("div");return c.append(E),c}}else{let c=document.createElement("div");for(let d of[...E])c.append(d);return c}}class T{constructor(c){this.originalNode=c,this.realParentNode=c.parentNode,this.previousSibling=c.previousSibling,this.nextSibling=c.nextSibling}get childNodes(){let c=[],d=this.previousSibling?this.previousSibling.nextSibling:this.realParentNode.firstChild;for(;d&&d!=this.nextSibling;)c.push(d),d=d.nextSibling;return c}querySelectorAll(c){return this.childNodes.reduce((d,u)=>{if(u instanceof Element){u.matches(c)&&d.push(u);let l=u.querySelectorAll(c);for(let g=0;g<l.length;g++)d.push(l[g])}return d},[])}insertBefore(c,d){return this.realParentNode.insertBefore(c,d)}moveBefore(c,d){return this.realParentNode.moveBefore(c,d)}get __idiomorphRoot(){return this.originalNode}}function _(E){let c=new DOMParser,d=E.replace(/<svg(\s[^>]*>|>)([\s\S]*?)<\/svg>/gim,"");if(d.match(/<\/html>/)||d.match(/<\/head>/)||d.match(/<\/body>/)){let u=c.parseFromString(E,"text/html");if(d.match(/<\/html>/))return b.add(u),u;{let l=u.firstChild;return l&&b.add(l),l}}else{let l=c.parseFromString("<body><template>"+E+"</template></body>","text/html").body.querySelector("template").content;return b.add(l),l}}return{normalizeElement:A,normalizeParent:h}}();return{morph:n,defaults:e}}();var Ht={type:2,name:F.MergeFragments,onGlobalInit:async t=>{let e=document.createElement("template");J(F.MergeFragments,({fragments:n="<div></div>",selector:r="",mergeMode:i=st,useViewTransition:o=`${Re}`})=>{let s=X(o);e.innerHTML=n.trim();let a=[...e.content.children];for(let p of a){if(!(p instanceof Element))throw $("NoFragmentsFound",t);let y=r||`#${p.getAttribute("id")}`,v=[...document.querySelectorAll(y)||[]];if(!v.length)throw $("NoTargetsFound",t,{selectorOrID:y});s&&oe?Ae.startViewTransition(()=>Ot(t,i,p,v)):Ot(t,i,p,v)}})}};function Ot(t,e,n,r){for(let i of r){i.dataset.fragmentMergeTarget="true";let o=n.cloneNode(!0);switch(e){case U.Morph:{be(o,s=>{!s.id?.length&&Object.keys(s.dataset).length&&(s.id=we(s));let a=t.removals.get(s.id);if(a){let p=new Map;for(let[y,v]of a){let k=Me(y,y);p.set(k,v),a.delete(y)}t.removals.set(s.id,p)}}),Dt.morph(i,o);break}case U.Inner:i.innerHTML=o.outerHTML;break;case U.Outer:i.replaceWith(o);break;case U.Prepend:i.prepend(o);break;case U.Append:i.append(o);break;case U.Before:i.before(o);break;case U.After:i.after(o);break;case U.UpsertAttributes:for(let s of o.getAttributeNames()){let a=o.getAttribute(s);i.setAttribute(s,a)}break;default:throw $("InvalidMergeMode",t,{mergeMode:e})}}}var Ft={type:2,name:F.MergeSignals,onGlobalInit:async t=>{J(F.MergeSignals,({signals:e="{}",onlyIfMissing:n=`${it}`})=>{let{signals:r}=t,i=X(n);r.merge(xe(e),i)})}};var qt={type:2,name:F.RemoveFragments,onGlobalInit:async t=>{J(F.RemoveFragments,({selector:e,useViewTransition:n=`${Re}`})=>{if(!e.length)throw $("NoSelectorProvided",t);let r=X(n),i=document.querySelectorAll(e),o=()=>{for(let s of i)s.remove()};r&&oe?Ae.startViewTransition(()=>o()):o()})}};var $t={type:2,name:F.RemoveSignals,onGlobalInit:async t=>{J(F.RemoveSignals,({paths:e=""})=>{let n=e.split(`
`).map(r=>r.trim());if(!n?.length)throw $("NoPathsProvided",t);t.signals.remove(...n)})}};var Wt={type:3,name:"clipboard",fn:(t,e)=>{if(!navigator.clipboard)throw P("ClipboardNotAvailable",t);navigator.clipboard.writeText(e)}};var Bt={type:1,name:"customValidity",keyReq:2,valReq:1,onLoad:t=>{let{el:e,genRX:n,effect:r}=t;if(!(e instanceof HTMLInputElement||e instanceof HTMLSelectElement||e instanceof HTMLTextAreaElement))throw P("CustomValidityInvalidElement",t);let i=n();return r(()=>{let o=i();if(typeof o!="string")throw P("CustomValidityInvalidExpression",t,{result:o});e.setCustomValidity(o)})}};function se(t){if(!t||t.size<=0)return 0;for(let e of t){if(e.endsWith("ms"))return Number(e.replace("ms",""));if(e.endsWith("s"))return Number(e.replace("s",""))*1e3;try{return Number.parseFloat(e)}catch{}}return 0}function ae(t,e,n=!1){return t?t.has(e.toLowerCase()):n}function Ln(t,e,n=!1,r=!0){let i=-1,o=()=>i&&clearTimeout(i);return(...s)=>{o(),n&&!i&&t(...s),i=setTimeout(()=>{r&&t(...s),o()},e)}}function Dn(t,e,n=!0,r=!1){let i=!1;return(...o)=>{i||(n&&t(...o),i=!0,setTimeout(()=>{i=!1,r&&t(...o)},e))}}function ee(t,e){let n=e.get("debounce");if(n){let i=se(n),o=ae(n,"leading",!1),s=!ae(n,"notrail",!1);t=Ln(t,i,o,s)}let r=e.get("throttle");if(r){let i=se(r),o=!ae(r,"noleading",!1),s=ae(r,"trail",!1);t=Dn(t,i,o,s)}return t}var Gt={type:1,name:"onIntersect",keyReq:2,onLoad:({el:t,rawKey:e,mods:n,genRX:r})=>{let i=ee(r(),n);i=W(i,n);let o={threshold:0};n.has("full")?o.threshold=1:n.has("half")&&(o.threshold=.5);let s=new IntersectionObserver(a=>{for(let p of a)p.isIntersecting&&(i(),n.has("once")&&(s.disconnect(),delete t.dataset[e]))},o);return s.observe(t),()=>s.disconnect()}};var Ut={type:1,name:"onInterval",keyReq:2,valReq:1,onLoad:({mods:t,genRX:e})=>{let n=W(e(),t),r=1e3,i=t.get("duration");i&&(r=se(i),ae(i,"leading",!1)&&n());let o=setInterval(n,r);return()=>{clearInterval(o)}}};var jt={type:1,name:"onLoad",keyReq:2,valReq:1,onLoad:({mods:t,genRX:e})=>{let n=W(e(),t),r=0,i=t.get("delay");return i&&(r=se(i)),setTimeout(n,r),()=>{}}};var Kt={type:1,name:"onRaf",keyReq:2,valReq:1,onLoad:({mods:t,genRX:e})=>{let n=ee(e(),t);n=W(n,t);let r,i=()=>{n(),r=requestAnimationFrame(i)};return r=requestAnimationFrame(i),()=>{r&&cancelAnimationFrame(r)}}};function Ye(t,e){return e=e.replaceAll(".","\\.").replaceAll("**",le).replaceAll("*","[^\\.]*").replaceAll(le,".*"),new RegExp(`^${e}$`).test(t)}function he(t,e){let n=[],r=e.split(/\s+/).filter(i=>i!=="");r=r.map(i=>Z(i));for(let i of r)t.walk(o=>{Ye(o,i)&&n.push(o)});return n}var Jt={type:1,name:"onSignalChange",valReq:1,onLoad:({key:t,mods:e,signals:n,genRX:r})=>{let i=ee(r(),e);if(i=W(i,e),t===""){let a=p=>i(p);return document.addEventListener(ye,a),()=>{document.removeEventListener(ye,a)}}let o=H(t,e),s=new Map;return n.walk((a,p)=>{Ye(a,o)&&s.set(p,p.value)}),me(()=>{for(let[a,p]of s)p!==a.value&&(i(),s.set(a,a.value))})}};var zt={type:1,name:"persist",keyReq:2,onLoad:({effect:t,mods:e,signals:n,value:r})=>{let i=q,o=e.has("session")?sessionStorage:localStorage,s=r!==""?r:"**",a=()=>{let y=o.getItem(i)||"{}",v=JSON.parse(y);n.merge(v)},p=()=>{let y=he(n,s),v=n.subset(...y);o.setItem(i,JSON.stringify(v))};return a(),t(()=>{p()})}};var Yt={type:1,name:"replaceUrl",keyReq:2,valReq:1,onLoad:({effect:t,genRX:e})=>{let n=e();return t(()=>{let r=n(),i=window.location.href,o=new URL(r,i).toString();window.history.replaceState({},"",o)})}};var Xe="smooth",Xt="instant",Zt="auto",On="hstart",Hn="hcenter",Fn="hend",qn="hnearest",$n="vstart",Wn="vcenter",Bn="vend",Gn="vnearest",He="center",Qt="start",en="end",tn="nearest",Un="focus",nn={type:1,name:"scrollIntoView",keyReq:2,valReq:2,onLoad:t=>{let{el:e,mods:n,rawKey:r}=t;e.tabIndex||e.setAttribute("tabindex","0");let i={behavior:Xe,block:He,inline:He};if(n.has(Xe)&&(i.behavior=Xe),n.has(Xt)&&(i.behavior=Xt),n.has(Zt)&&(i.behavior=Zt),n.has(On)&&(i.inline=Qt),n.has(Hn)&&(i.inline=He),n.has(Fn)&&(i.inline=en),n.has(qn)&&(i.inline=tn),n.has($n)&&(i.block=Qt),n.has(Wn)&&(i.block=He),n.has(Bn)&&(i.block=en),n.has(Gn)&&(i.block=tn),!(e instanceof HTMLElement||e instanceof SVGElement))throw P("ScrollIntoViewInvalidElement",t);e.tabIndex||e.setAttribute("tabindex","0"),e.scrollIntoView(i),n.has(Un)&&e.focus(),delete e.dataset[r]}};var rn="view-transition",on={type:1,name:"viewTransition",keyReq:2,valReq:1,onGlobalInit(){let t=!1;for(let e of document.head.childNodes)e instanceof HTMLMetaElement&&e.name===rn&&(t=!0);if(!t){let e=document.createElement("meta");e.name=rn,e.content="same-origin",document.head.appendChild(e)}},onLoad:({effect:t,el:e,genRX:n})=>{if(!oe){console.error("Browser does not support view transitions");return}let r=n();return t(()=>{let i=r();if(!i?.length)return;let o=e.style;o.viewTransitionName=i})}};var sn={type:1,name:"attr",valReq:1,onLoad:({el:t,key:e,effect:n,genRX:r})=>{let i=r();return e===""?n(async()=>{let o=i();for(let[s,a]of Object.entries(o))a===!1?t.removeAttribute(s):t.setAttribute(s,a)}):(e=Y(e),n(async()=>{let o=!1;try{o=i()}catch{}let s;typeof o=="string"?s=o:s=JSON.stringify(o),!s||s==="false"||s==="null"||s==="undefined"?t.removeAttribute(e):t.setAttribute(e,s)}))}};var jn=/^data:(?<mime>[^;]+);base64,(?<contents>.*)$/,an=["change","input","keydown"],ln={type:1,name:"bind",keyReq:3,valReq:3,onLoad:t=>{let{el:e,key:n,mods:r,signals:i,value:o,effect:s}=t,a=e,p=n?H(n,r):Z(o),y=e.tagName.toLowerCase(),v=y.includes("input"),k=y.includes("select"),b=e.getAttribute("type"),A=e.hasAttribute("value"),h="",T=v&&b==="checkbox";T&&(h=A?"":!1);let _=v&&b==="number";_&&(h=0);let E=v&&b==="radio";E&&(e.getAttribute("name")?.length||e.setAttribute("name",p));let c=v&&b==="file",{signal:d,inserted:u}=i.upsertIfMissing(p,h),l=-1;Array.isArray(d.value)&&(e.getAttribute("name")===null&&e.setAttribute("name",p),l=[...document.querySelectorAll(`[name="${p}"]`)].findIndex(M=>M===t.el));let g=l>=0,m=()=>[...i.value(p)],f=()=>{let M=i.value(p);g&&!k&&(M=M[l]||h);let L=`${M}`;if(T||E)typeof M=="boolean"?a.checked=M:a.checked=L===a.value;else if(k){let N=e;if(N.multiple){if(!g)throw P("BindSelectMultiple",t);for(let O of N.options){if(O?.disabled)return;let D=_?Number(O.value):O.value;O.selected=M.includes(D)}}else N.value=L}else c||("value"in e?e.value=L:e.setAttribute("value",L))},S=async()=>{let M=i.value(p);if(g){let D=M;for(;l>=D.length;)D.push(h);M=D[l]||h}let L=(D,B)=>{let G=B;g&&!k&&(G=m(),G[l]=B),i.setValue(D,G)};if(c){let D=[...a?.files||[]],B=[],G=[],Ze=[];await Promise.all(D.map(Qe=>new Promise(bn=>{let te=new FileReader;te.onload=()=>{if(typeof te.result!="string")throw P("InvalidFileResultType",t,{resultType:typeof te.result});let Fe=te.result.match(jn);if(!Fe?.groups)throw P("InvalidDataUri",t,{result:te.result});B.push(Fe.groups.contents),G.push(Fe.groups.mime),Ze.push(Qe.name)},te.onloadend=()=>bn(void 0),te.readAsDataURL(Qe)}))),L(p,B),L(`${p}Mimes`,G),L(`${p}Names`,Ze);return}let N=a.value||"",O;if(T){let D=a.checked||a.getAttribute("checked")==="true";A?O=D?N:"":O=D}else if(k){let B=[...e.selectedOptions];g?O=B.filter(G=>G.selected).map(G=>G.value):O=B[0]?.value||h}else typeof M=="boolean"?O=!!N:typeof M=="number"?O=Number(N):O=N||"";L(p,O)};u&&S();for(let M of an)e.addEventListener(M,S);let w=M=>{M.persisted&&S()};window.addEventListener("pageshow",w);let R=s(()=>f());return()=>{R();for(let M of an)e.removeEventListener(M,S);window.removeEventListener("pageshow",w)}}};var un={type:1,name:"class",valReq:1,onLoad:({el:t,key:e,mods:n,effect:r,genRX:i})=>{let o=t.classList,s=i();return r(()=>{if(e===""){let a=s();for(let[p,y]of Object.entries(a)){let v=p.split(/\s+/);y?o.add(...v):o.remove(...v)}}else{let a=Y(e);a=H(a,n),s()?o.add(a):o.remove(a)}})}};var cn={type:1,name:"on",keyReq:1,valReq:1,argNames:["evt"],onLoad:({el:t,key:e,mods:n,genRX:r})=>{let i=r(),o=t;n.has("window")&&(o=window);let s=v=>{v&&((n.has("prevent")||e==="submit")&&v.preventDefault(),n.has("stop")&&v.stopPropagation()),i(v)};s=ee(s,n),s=W(s,n);let a={capture:!1,passive:!1,once:!1};if(n.has("capture")&&(a.capture=!0),n.has("passive")&&(a.passive=!0),n.has("once")&&(a.once=!0),n.has("outside")){o=document;let v=s;s=b=>{let A=b?.target;t.contains(A)||v(b)}}let y=Y(e);return y=H(y,n),y===ie&&(o=document),o.addEventListener(y,s,a),()=>{o.removeEventListener(y,s)}}};var fn={type:1,name:"ref",keyReq:3,valReq:3,onLoad:({el:t,key:e,mods:n,signals:r,value:i})=>{let o=e?H(e,n):Z(i);r.setValue(o,t)}};var dn="none",pn="display",mn={type:1,name:"show",keyReq:2,valReq:1,onLoad:({el:{style:t},genRX:e,effect:n})=>{let r=e();return n(async()=>{r()?t.display===dn&&t.removeProperty(pn):t.setProperty(pn,dn)})}};var gn={type:1,name:"text",keyReq:2,valReq:1,onLoad:t=>{let{el:e,effect:n,genRX:r}=t,i=r();return e instanceof HTMLElement||P("TextInvalidElement",t),n(()=>{let o=i(t);e.textContent=`${o}`})}};var{round:Kn,max:Jn,min:zn}=Math,hn={type:3,name:"fit",fn:(t,e,n,r,i,o,s=!1,a=!1)=>{let p=(e-n)/(r-n)*(o-i)+i;return a&&(p=Kn(p)),s&&(p=Jn(i,zn(o,p))),p}};var yn={type:3,name:"setAll",fn:({signals:t},e,n)=>{let r=he(t,e);for(let i of r)t.setValue(i,n)}};var vn={type:3,name:"toggleAll",fn:({signals:t},e)=>{let n=he(t,e);for(let r of n)t.setValue(r,!t.value(r))}};Ve(sn,ln,un,cn,fn,mn,gn,Vt,Ct,kt,It,Nt,Pt,Ht,Ft,qt,$t,Lt,Wt,Bt,Gt,Ut,jt,Kt,Jt,zt,Yt,nn,on,hn,yn,vn);ze();export{ze as apply,Ve as load,Et as setAlias};
//# sourceMappingURL=datastar.js.map

{".class": "MypyFile", "_fullname": "limits.storage.redis_sentinel", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ConfigurationError": {".class": "SymbolTableNode", "cross_ref": "limits.errors.ConfigurationError", "kind": "Gdef"}, "RedisClient": {".class": "SymbolTableNode", "cross_ref": "limits.typing.RedisClient", "kind": "Gdef"}, "RedisSentinelStorage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["limits.storage.redis.RedisStorage"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage", "name": "RedisSentinelStorage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage", "has_param_spec_type": false, "metaclass_type": "limits.storage.registry.StorageRegistry", "metadata": {}, "module_name": "limits.storage.redis_sentinel", "mro": ["limits.storage.redis_sentinel.RedisSentinelStorage", "limits.storage.redis.RedisStorage", "limits.storage.base.Storage", "limits.util.LazyDependency", "limits.storage.base.MovingWindowSupport", "limits.storage.base.SlidingWindowCounterSupport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "DEPENDENCIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.DEPENDENCIES", "name": "DEPENDENCIES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["packaging.version.Version", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "STORAGE_SCHEME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.STORAGE_SCHEME", "name": "STORAGE_SCHEME", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "uri", "service_name", "use_replicas", "sentinel_kwargs", "key_prefix", "wrap_exceptions", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "uri", "service_name", "use_replicas", "sentinel_kwargs", "key_prefix", "wrap_exceptions", "options"], "arg_types": ["limits.storage.redis_sentinel.RedisSentinelStorage", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.float", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RedisSentinelStorage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.base_exceptions", "name": "base_exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["limits.storage.redis_sentinel.RedisSentinelStorage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base_exceptions of RedisSentinelStorage", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.base_exceptions", "name": "base_exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["limits.storage.redis_sentinel.RedisSentinelStorage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base_exceptions of RedisSentinelStorage", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "readonly"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.get_connection", "name": "get_connection", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "readonly"], "arg_types": ["limits.storage.redis_sentinel.RedisSentinelStorage", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_connection of RedisSentinelStorage", "ret_type": "limits.typing.RedisClientP", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sentinel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.sentinel", "name": "sentinel", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "service_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.service_name", "name": "service_name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "storage": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.storage", "name": "storage", "type": "limits.typing.RedisClientP"}}, "storage_slave": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.storage_slave", "name": "storage_slave", "type": "limits.typing.RedisClientP"}}, "use_replicas": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.use_replicas", "name": "use_replicas", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "limits.storage.redis_sentinel.RedisSentinelStorage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "limits.storage.redis_sentinel.RedisSentinelStorage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RedisStorage": {".class": "SymbolTableNode", "cross_ref": "limits.storage.redis.RedisStorage", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Version": {".class": "SymbolTableNode", "cross_ref": "packaging.version.Version", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis_sentinel.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis_sentinel.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis_sentinel.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis_sentinel.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis_sentinel.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis_sentinel.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "urllib": {".class": "SymbolTableNode", "cross_ref": "urllib", "kind": "Gdef"}, "versionchanged": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "limits.storage.redis_sentinel.versionchanged", "name": "versionchanged", "type": {".class": "AnyType", "missing_import_name": "limits.storage.redis_sentinel.versionchanged", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\storage\\redis_sentinel.py"}
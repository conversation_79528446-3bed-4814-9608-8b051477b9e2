{"data_mtime": 1752571434, "dep_lines": [12, 17, 11, 1, 3, 4, 5, 6, 7, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9], "dep_prios": [5, 5, 10, 5, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["limits.aio.storage.base", "limits.storage.base", "limits.typing", "__future__", "asyncio", "bisect", "time", "collections", "math", "limits", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "asyncio.events", "asyncio.locks", "asyncio.mixins", "limits.storage", "limits.storage.registry", "limits.util", "typing"], "hash": "720ec2ab47a8866d80d815f0d4f4a4effc00b3d6", "id": "limits.aio.storage.memory", "ignore_all": true, "interface_hash": "6af833b2e15ae318f6da645ba695c97b53efff1b", "mtime": 1752570205, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\aio\\storage\\memory.py", "plugin_data": null, "size": 9842, "suppressed": ["deprecated.sphinx"], "version_id": "1.15.0"}
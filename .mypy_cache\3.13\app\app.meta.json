{"data_mtime": 1752571671, "dep_lines": [4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 16, 17, 20, 21, 23, 45, 75, 76, 1, 2, 3, 19, 22, 74, 745, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 18], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["fastapi.middleware.cors", "starlette.middleware.sessions", "app.db", "app.schemas", "app.users", "app.user_settings_db", "app.config", "app.security", "app.logging_config", "fastapi.responses", "fastapi.staticfiles", "jinja2_fragments.fastapi", "datastar_py.fastapi", "datastar_py.sse", "app.calc01", "urllib.parse", "slowapi.util", "slowapi.errors", "contextlib", "<PERSON><PERSON><PERSON>", "typing", "httpx", "time", "<PERSON><PERSON><PERSON>", "traceback", "builtins", "_collections_abc", "_frozen_importlib", "_ssl", "_typeshed", "abc", "datastar_py", "datastar_py.starlette", "enum", "fastapi.applications", "fastapi.param_functions", "fastapi.params", "fastapi.routing", "fastapi_users", "fastapi_users.authentication", "fastapi_users.authentication.backend", "fastapi_users.authentication.transport", "fastapi_users.authentication.transport.base", "fastapi_users.authentication.transport.cookie", "fastapi_users.db", "fastapi_users.db.base", "fastapi_users.fastapi_users", "fastapi_users.schemas", "fastapi_users_db_sqlalchemy", "http", "http.cookiejar", "httpx._auth", "httpx._client", "httpx._config", "httpx._models", "httpx._transports", "httpx._transports.base", "httpx._urls", "jinja2", "jinja2.environment", "jinja2.runtime", "jinja2_fragments", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic.types", "slowapi.extension", "slowapi.wrappers", "sqlalchemy", "sqlalchemy.inspection", "sqlalchemy.orm", "sqlalchemy.orm.decl_api", "ssl", "starlette", "starlette._utils", "starlette.applications", "starlette.background", "starlette.datastructures", "starlette.exceptions", "starlette.middleware", "starlette.middleware.cors", "starlette.requests", "starlette.responses", "starlette.routing", "starlette.staticfiles", "starlette.templating", "starlette.websockets", "urllib", "uuid"], "hash": "563f345573209310c63d4348bb75fdab9d84d97a", "id": "app.app", "ignore_all": false, "interface_hash": "eb806c8cad7121eda0762d611fe53339ca0762cb", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\app.py", "plugin_data": null, "size": 39054, "suppressed": ["jinja_partials"], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "datastar_py.consts", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ATTRIBUTES_DATALINE_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.ATTRIBUTES_DATALINE_LITERAL", "name": "ATTRIBUTES_DATALINE_LITERAL", "type": "builtins.str"}}, "AUTO_REMOVE_DATALINE_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.AUTO_REMOVE_DATALINE_LITERAL", "name": "AUTO_REMOVE_DATALINE_LITERAL", "type": "builtins.str"}}, "DATASTAR_KEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.DATASTAR_KEY", "name": "DATASTAR_KEY", "type": "builtins.str"}}, "DEFAULT_EXECUTE_SCRIPT_ATTRIBUTES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.DEFAULT_EXECUTE_SCRIPT_ATTRIBUTES", "name": "DEFAULT_EXECUTE_SCRIPT_ATTRIBUTES", "type": "builtins.str"}}, "DEFAULT_EXECUTE_SCRIPT_AUTO_REMOVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.DEFAULT_EXECUTE_SCRIPT_AUTO_REMOVE", "name": "DEFAULT_EXECUTE_SCRIPT_AUTO_REMOVE", "type": "builtins.bool"}}, "DEFAULT_FRAGMENTS_USE_VIEW_TRANSITIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.DEFAULT_FRAGMENTS_USE_VIEW_TRANSITIONS", "name": "DEFAULT_FRAGMENTS_USE_VIEW_TRANSITIONS", "type": "builtins.bool"}}, "DEFAULT_MERGE_SIGNALS_ONLY_IF_MISSING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.DEFAULT_MERGE_SIGNALS_ONLY_IF_MISSING", "name": "DEFAULT_MERGE_SIGNALS_ONLY_IF_MISSING", "type": "builtins.bool"}}, "DEFAULT_SSE_RETRY_DURATION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.DEFAULT_SSE_RETRY_DURATION", "name": "DEFAULT_SSE_RETRY_DURATION", "type": "builtins.int"}}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "EventType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.consts.StrEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.consts.EventType", "name": "EventType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "datastar_py.consts.EventType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "datastar_py.consts", "mro": ["datastar_py.consts.EventType", "datastar_py.consts.StrEnum", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "EXECUTE_SCRIPT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.EventType.EXECUTE_SCRIPT", "name": "EXECUTE_SCRIPT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "datastar-execute-script"}, "type_ref": "builtins.str"}}}, "MERGE_FRAGMENTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.EventType.MERGE_FRAGMENTS", "name": "MERGE_FRAGMENTS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "datastar-merge-fragments"}, "type_ref": "builtins.str"}}}, "MERGE_SIGNALS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.EventType.MERGE_SIGNALS", "name": "MERGE_SIGNALS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "datastar-merge-signals"}, "type_ref": "builtins.str"}}}, "REMOVE_FRAGMENTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.EventType.REMOVE_FRAGMENTS", "name": "REMOVE_FRAGMENTS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "datastar-remove-fragments"}, "type_ref": "builtins.str"}}}, "REMOVE_SIGNALS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.EventType.REMOVE_SIGNALS", "name": "REMOVE_SIGNALS", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "datastar-remove-signals"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.consts.EventType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.consts.EventType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FRAGMENTS_DATALINE_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.FRAGMENTS_DATALINE_LITERAL", "name": "FRAGMENTS_DATALINE_LITERAL", "type": "builtins.str"}}, "FragmentMergeMode": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["datastar_py.consts.StrEnum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.consts.FragmentMergeMode", "name": "FragmentMergeMode", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "datastar_py.consts.FragmentMergeMode", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "datastar_py.consts", "mro": ["datastar_py.consts.FragmentMergeMode", "datastar_py.consts.StrEnum", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "AFTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.FragmentMergeMode.AFTER", "name": "AFTER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "after"}, "type_ref": "builtins.str"}}}, "APPEND": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.FragmentMergeMode.APPEND", "name": "APPEND", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "append"}, "type_ref": "builtins.str"}}}, "BEFORE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.FragmentMergeMode.BEFORE", "name": "BEFORE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "before"}, "type_ref": "builtins.str"}}}, "INNER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.FragmentMergeMode.INNER", "name": "INNER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "inner"}, "type_ref": "builtins.str"}}}, "MORPH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.FragmentMergeMode.MORPH", "name": "MORPH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "morph"}, "type_ref": "builtins.str"}}}, "OUTER": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.FragmentMergeMode.OUTER", "name": "OUTER", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "outer"}, "type_ref": "builtins.str"}}}, "PREPEND": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.FragmentMergeMode.PREPEND", "name": "PREPEND", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "prepend"}, "type_ref": "builtins.str"}}}, "UPSERT_ATTRIBUTES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.FragmentMergeMode.UPSERT_ATTRIBUTES", "name": "UPSERT_ATTRIBUTES", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "upsertAttributes"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.consts.FragmentMergeMode.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.consts.FragmentMergeMode", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MERGE_MODE_DATALINE_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.MERGE_MODE_DATALINE_LITERAL", "name": "MERGE_MODE_DATALINE_LITERAL", "type": "builtins.str"}}, "ONLY_IF_MISSING_DATALINE_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.ONLY_IF_MISSING_DATALINE_LITERAL", "name": "ONLY_IF_MISSING_DATALINE_LITERAL", "type": "builtins.str"}}, "PATHS_DATALINE_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.PATHS_DATALINE_LITERAL", "name": "PATHS_DATALINE_LITERAL", "type": "builtins.str"}}, "SCRIPT_DATALINE_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.SCRIPT_DATALINE_LITERAL", "name": "SCRIPT_DATALINE_LITERAL", "type": "builtins.str"}}, "SELECTOR_DATALINE_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.SELECTOR_DATALINE_LITERAL", "name": "SELECTOR_DATALINE_LITERAL", "type": "builtins.str"}}, "SIGNALS_DATALINE_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.SIGNALS_DATALINE_LITERAL", "name": "SIGNALS_DATALINE_LITERAL", "type": "builtins.str"}}, "StrEnum": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.str", "enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "datastar_py.consts.StrEnum", "name": "StrEnum", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "datastar_py.consts.StrEnum", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "datastar_py.consts", "mro": ["datastar_py.consts.StrEnum", "builtins.str", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "__format__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.StrEnum.__format__", "name": "__format__", "type": null}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.StrEnum.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "datastar_py.consts.StrEnum.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "datastar_py.consts.StrEnum", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "USE_VIEW_TRANSITION_DATALINE_LITERAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.USE_VIEW_TRANSITION_DATALINE_LITERAL", "name": "USE_VIEW_TRANSITION_DATALINE_LITERAL", "type": "builtins.str"}}, "VERSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "datastar_py.consts.VERSION", "name": "VERSION", "type": "builtins.str"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.consts.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.consts.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.consts.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.consts.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.consts.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "datastar_py.consts.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\datastar_py\\consts.py"}
{"data_mtime": 1752598333, "dep_lines": [5, 1, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "json", "re", "typing", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "types"], "hash": "97ab4f25e05dce2e54e167073f75402788d2aba8", "id": "datastar_py.attributes", "ignore_all": true, "interface_hash": "b0779dc877a23b51202b6b5095b290520b5c25b7", "mtime": 1752598274, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\datastar_py\\attributes.py", "plugin_data": null, "size": 18884, "suppressed": [], "version_id": "1.15.0"}
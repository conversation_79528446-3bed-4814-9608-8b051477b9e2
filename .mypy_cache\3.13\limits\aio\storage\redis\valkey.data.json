{".class": "MypyFile", "_fullname": "limits.aio.storage.redis.valkey", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "RedispyBridge": {".class": "SymbolTableNode", "cross_ref": "limits.aio.storage.redis.redispy.RedispyBridge", "kind": "Gdef"}, "ValkeyBridge": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["limits.aio.storage.redis.redispy.RedispyBridge"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "limits.aio.storage.redis.valkey.ValkeyBridge", "name": "<PERSON>keyBridge", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "limits.aio.storage.redis.valkey.ValkeyBridge", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "limits.aio.storage.redis.valkey", "mro": ["limits.aio.storage.redis.valkey.ValkeyBridge", "limits.aio.storage.redis.redispy.RedispyBridge", "limits.aio.storage.redis.bridge.RedisBridge", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "base_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "limits.aio.storage.redis.valkey.ValkeyBridge.base_exceptions", "name": "base_exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["limits.aio.storage.redis.valkey.ValkeyBridge"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base_exceptions of ValkeyBridge", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "limits.aio.storage.redis.valkey.ValkeyBridge.base_exceptions", "name": "base_exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["limits.aio.storage.redis.valkey.ValkeyBridge"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base_exceptions of ValkeyBridge", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "limits.aio.storage.redis.valkey.ValkeyBridge.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "limits.aio.storage.redis.valkey.ValkeyBridge", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.redis.valkey.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.redis.valkey.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.redis.valkey.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.redis.valkey.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.redis.valkey.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.aio.storage.redis.valkey.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\aio\\storage\\redis\\valkey.py"}
{"data_mtime": 1752571434, "dep_lines": [12, 6, 7, 11, 17, 25, 1, 3, 4, 5, 6, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 5, 5, 5, 10, 10, 10, 20, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["limits.storage.base", "urllib.parse", "collections.abc", "limits.errors", "limits.typing", "limits.util", "__future__", "inspect", "threading", "time", "urllib", "math", "types", "builtins", "_collections_abc", "_frozen_importlib", "_thread", "_typeshed", "abc", "collections", "limits.storage.registry", "packaging", "packaging.version", "typing"], "hash": "08fc76c06661ec4285bd9518ad27c7b8d4053a52", "id": "limits.storage.memcached", "ignore_all": true, "interface_hash": "f35c13682143af5afe804b90914405e864027e1c", "mtime": 1752570205, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\storage\\memcached.py", "plugin_data": null, "size": 10455, "suppressed": [], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "limits.storage.redis", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MovingWindowSupport": {".class": "SymbolTableNode", "cross_ref": "limits.storage.base.MovingWindowSupport", "kind": "Gdef"}, "RedisClient": {".class": "SymbolTableNode", "cross_ref": "limits.typing.RedisClient", "kind": "Gdef"}, "RedisStorage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["limits.storage.base.Storage", "limits.storage.base.MovingWindowSupport", "limits.storage.base.SlidingWindowCounterSupport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "limits.storage.redis.RedisStorage", "name": "RedisStorage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage", "has_param_spec_type": false, "metaclass_type": "limits.storage.registry.StorageRegistry", "metadata": {}, "module_name": "limits.storage.redis", "mro": ["limits.storage.redis.RedisStorage", "limits.storage.base.Storage", "limits.util.LazyDependency", "limits.storage.base.MovingWindowSupport", "limits.storage.base.SlidingWindowCounterSupport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "DEPENDENCIES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis.RedisStorage.DEPENDENCIES", "name": "DEPENDENCIES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["packaging.version.Version", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "PREFIX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis.RedisStorage.PREFIX", "name": "PREFIX", "type": "builtins.str"}}, "RES_DIR": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis.RedisStorage.RES_DIR", "name": "RES_DIR", "type": "builtins.str"}}, "SCRIPT_ACQUIRE_MOVING_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis.RedisStorage.SCRIPT_ACQUIRE_MOVING_WINDOW", "name": "SCRIPT_ACQUIRE_MOVING_WINDOW", "type": "builtins.bytes"}}, "SCRIPT_ACQUIRE_SLIDING_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis.RedisStorage.SCRIPT_ACQUIRE_SLIDING_WINDOW", "name": "SCRIPT_ACQUIRE_SLIDING_WINDOW", "type": "builtins.bytes"}}, "SCRIPT_CLEAR_KEYS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis.RedisStorage.SCRIPT_CLEAR_KEYS", "name": "SCRIPT_CLEAR_KEYS", "type": "builtins.bytes"}}, "SCRIPT_INCR_EXPIRE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis.RedisStorage.SCRIPT_INCR_EXPIRE", "name": "SCRIPT_INCR_EXPIRE", "type": "builtins.bytes"}}, "SCRIPT_MOVING_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis.RedisStorage.SCRIPT_MOVING_WINDOW", "name": "SCRIPT_MOVING_WINDOW", "type": "builtins.bytes"}}, "SCRIPT_SLIDING_WINDOW": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis.RedisStorage.SCRIPT_SLIDING_WINDOW", "name": "SCRIPT_SLIDING_WINDOW", "type": "builtins.bytes"}}, "STORAGE_SCHEME": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "limits.storage.redis.RedisStorage.STORAGE_SCHEME", "name": "STORAGE_SCHEME", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "uri", "connection_pool", "key_prefix", "wrap_exceptions", "options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 4], "arg_names": ["self", "uri", "connection_pool", "key_prefix", "wrap_exceptions", "options"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": "limits.storage.redis.redis", "source_any": null, "type_of_any": 3}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.bool", {".class": "UnionType", "items": ["builtins.float", "builtins.str", "builtins.bool"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RedisStorage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_current_window_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage._current_window_key", "name": "_current_window_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_current_window_key of RedisStorage", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_previous_window_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage._previous_window_key", "name": "_previous_window_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_previous_window_key of RedisStorage", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "acquire_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "key", "limit", "expiry", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.acquire_entry", "name": "acquire_entry", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "key", "limit", "expiry", "amount"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "acquire_entry of RedisStorage", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "acquire_sliding_window_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "key", "limit", "expiry", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.acquire_sliding_window_entry", "name": "acquire_sliding_window_entry", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "key", "limit", "expiry", "amount"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "acquire_sliding_window_entry of RedisStorage", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "base_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "limits.storage.redis.RedisStorage.base_exceptions", "name": "base_exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["limits.storage.redis.RedisStorage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base_exceptions of RedisStorage", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "limits.storage.redis.RedisStorage.base_exceptions", "name": "base_exceptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["limits.storage.redis.RedisStorage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "base_exceptions of RedisStorage", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.Exception"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.Exception"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.check", "name": "check", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["limits.storage.redis.RedisStorage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check of RedisStorage", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of RedisStorage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_sliding_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "expiry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.clear_sliding_window", "name": "clear_sliding_window", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "expiry"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_sliding_window of RedisStorage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dependency": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "limits.storage.redis.RedisStorage.dependency", "name": "dependency", "type": "types.ModuleType"}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of RedisStorage", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "readonly"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.get_connection", "name": "get_connection", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "readonly"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_connection of RedisStorage", "ret_type": "limits.typing.RedisClientP", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_expiry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.get_expiry", "name": "get_expiry", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_expiry of RedisStorage", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_moving_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "key", "limit", "expiry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.get_moving_window", "name": "get_moving_window", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "key", "limit", "expiry"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_moving_window of RedisStorage", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_sliding_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "expiry"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.get_sliding_window", "name": "get_sliding_window", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "expiry"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_sliding_window of RedisStorage", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.float", "builtins.int", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "incr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "key", "expiry", "amount"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.incr", "name": "incr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "key", "expiry", "amount"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "incr of RedisStorage", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "initialize_storage": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "_uri"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.initialize_storage", "name": "initialize_storage", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "_uri"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "initialize_storage of RedisStorage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "limits.storage.redis.RedisStorage.key_prefix", "name": "key_prefix", "type": "builtins.str"}}, "lua_acquire_moving_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "limits.storage.redis.RedisStorage.lua_acquire_moving_window", "name": "lua_acquire_moving_window", "type": {".class": "AnyType", "missing_import_name": "limits.storage.redis.redis", "source_any": null, "type_of_any": 3}}}, "lua_acquire_sliding_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "limits.storage.redis.RedisStorage.lua_acquire_sliding_window", "name": "lua_acquire_sliding_window", "type": {".class": "AnyType", "missing_import_name": "limits.storage.redis.redis", "source_any": null, "type_of_any": 3}}}, "lua_clear_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "limits.storage.redis.RedisStorage.lua_clear_keys", "name": "lua_clear_keys", "type": {".class": "AnyType", "missing_import_name": "limits.typing.redis", "source_any": null, "type_of_any": 3}}}, "lua_incr_expire": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "limits.storage.redis.RedisStorage.lua_incr_expire", "name": "lua_incr_expire", "type": {".class": "AnyType", "missing_import_name": "limits.typing.redis", "source_any": null, "type_of_any": 3}}}, "lua_moving_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "limits.storage.redis.RedisStorage.lua_moving_window", "name": "lua_moving_window", "type": {".class": "AnyType", "missing_import_name": "limits.storage.redis.redis", "source_any": null, "type_of_any": 3}}}, "lua_sliding_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "limits.storage.redis.RedisStorage.lua_sliding_window", "name": "lua_sliding_window", "type": {".class": "AnyType", "missing_import_name": "limits.storage.redis.redis", "source_any": null, "type_of_any": 3}}}, "prefixed_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.prefixed_key", "name": "prefixed_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["limits.storage.redis.RedisStorage", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "prefixed_key of RedisStorage", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.storage.redis.RedisStorage.reset", "name": "reset", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["limits.storage.redis.RedisStorage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "reset of RedisStorage", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "storage": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "limits.storage.redis.RedisStorage.storage", "name": "storage", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "target_server": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "limits.storage.redis.RedisStorage.target_server", "name": "target_server", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "redis"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "v<PERSON><PERSON>"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "limits.storage.redis.RedisStorage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "limits.storage.redis.RedisStorage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SlidingWindowCounterSupport": {".class": "SymbolTableNode", "cross_ref": "limits.storage.base.SlidingWindowCounterSupport", "kind": "Gdef"}, "Storage": {".class": "SymbolTableNode", "cross_ref": "limits.storage.base.Storage", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Version": {".class": "SymbolTableNode", "cross_ref": "packaging.version.Version", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.storage.redis.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "get_package_data": {".class": "SymbolTableNode", "cross_ref": "limits.util.get_package_data", "kind": "Gdef"}, "redis": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "limits.storage.redis.redis", "name": "redis", "type": {".class": "AnyType", "missing_import_name": "limits.storage.redis.redis", "source_any": null, "type_of_any": 3}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "versionchanged": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "limits.storage.redis.versionchanged", "name": "versionchanged", "type": {".class": "AnyType", "missing_import_name": "limits.storage.redis.versionchanged", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\storage\\redis.py"}
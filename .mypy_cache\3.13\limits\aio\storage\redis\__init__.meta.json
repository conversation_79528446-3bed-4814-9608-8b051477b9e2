{"data_mtime": 1752571434, "dep_lines": [9, 10, 11, 12, 8, 6, 13, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 5], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["limits.aio.storage.redis.bridge", "limits.aio.storage.redis.coredis", "limits.aio.storage.redis.redispy", "limits.aio.storage.redis.valkey", "limits.aio.storage", "packaging.version", "limits.typing", "__future__", "asyncio", "builtins", "_frozen_importlib", "abc", "collections", "limits.aio.storage.base", "limits.storage", "limits.storage.registry", "limits.util", "packaging", "types", "typing"], "hash": "cf1b59c197a23f99398122bc8ffce5e2d3173ff0", "id": "limits.aio.storage.redis", "ignore_all": true, "interface_hash": "11eb5fe11d5a64f4fe230497414ba330fa30b46f", "mtime": 1752570205, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\aio\\storage\\redis\\__init__.py", "plugin_data": null, "size": 15032, "suppressed": ["deprecated.sphinx"], "version_id": "1.15.0"}
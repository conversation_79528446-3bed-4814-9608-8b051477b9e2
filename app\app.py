from contextlib import asynccontextmanager
from fastapi import <PERSON><PERSON><PERSON>, Request, Depends, Form
from typing import Annotated, Optional
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.sessions import SessionMiddleware
from app.db import create_db_and_tables, User
from app.schemas import UserCreate, UserRead, UserUpdate
from app.users import auth_backend, current_user, fastapi_users, get_user_manager
from app.user_settings_db import create_user_settings_db_and_tables, create_user_settings_entry, get_user_settings, update_user_settings, get_user_customers, add_customer_to_user_new, update_customer, delete_customer
from app.config import BASE_URL, is_development
from app.security import security_config, SecurityLogger, CSRFProtection, InputSanitizer, SecurityValidator
from app.config import SECRET
from app.logging_config import security_logger, app_logger, security_monitor

from fastapi.responses import RedirectResponse
from fastapi.staticfiles import StaticFiles
from jinja2_fragments.fastapi import Jinja2<PERSON>locks
import jinja_partials # type: ignore
import httpx
from datastar_py.fastapi import DatastarResponse
from datastar_py.sse import ServerSentEventGenerator as SSE
import time
from .calc01 import router as calc01_router

@asynccontextmanager
async def lifespan(_app: FastAPI):
    _ = _app  # Suppress unused argument warning
    # Not needed if you setup a migration system like Alembic
    await create_db_and_tables()
    await create_user_settings_db_and_tables()
    yield

app = FastAPI(lifespan=lifespan)
app.include_router(calc01_router)

# Mount static files early
app.mount("/static", StaticFiles(directory="static"), name="static")

# Allow all origins (for development only)
if is_development():
    # Development: Allow all origins for local network access
    origins = ["*"]
else:
    # Production: Use BASE_URL and common variations
    from urllib.parse import urlparse
    parsed_base = urlparse(BASE_URL)
    base_origin = f"{parsed_base.scheme}://{parsed_base.netloc}"

    origins = [
        base_origin,  # Main production URL
        "http://localhost",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
    ]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Session middleware for CSRF protection
app.add_middleware(
    SessionMiddleware,
    secret_key=str(SECRET.get_secret_value()),
    max_age=86400,  # 24 hours
    same_site="lax"  # Use "lax" for better compatibility
)

# Rate limiting setup (environment-aware)
if security_config.rate_limiting_enabled:
    from slowapi import Limiter, _rate_limit_exceeded_handler # type: ignore
    from slowapi.util import get_remote_address # type: ignore
    from slowapi.errors import RateLimitExceeded # type: ignore

    limiter = Limiter(key_func=get_remote_address)
    app.state.limiter = limiter
    app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler) # type: ignore

    print("✅ Rate limiting enabled for production")
else:
    # Mock limiter for development
    class MockLimiter:
        def limit(self, rate_limit: str):
            def decorator(func):
                return func
            return decorator

    limiter = MockLimiter() # type: ignore
    print("🔧 Rate limiting disabled for development")

# Log security configuration on startup
# Note: Using deprecated on_event for now, can be migrated to lifespan later
@app.on_event("startup") # type: ignore
async def startup_event():
    app_logger.info(f"🚀 Application starting in {security_config.environment} mode")

    security_features = []
    if security_config.rate_limiting_enabled:
        security_features.append("Rate Limiting")
    if security_config.csrf_protection_enabled:
        security_features.append("CSRF Protection")
    if security_config.strict_security_headers:
        security_features.append("Strict Security Headers")

    if security_features:
        app_logger.info(f"🔒 Security features enabled: {', '.join(security_features)}")
    else:
        app_logger.info("🔧 Running in development mode - security features relaxed")

    security_logger.info("Security monitoring initialized", extra={"event": "STARTUP"})

# Security headers middleware
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)

    if security_config.strict_security_headers:
        # Production security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = "default-src 'self' https:; script-src 'self' 'unsafe-inline' https:; style-src 'self' 'unsafe-inline' https:; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:;"
        response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
    else:
        # Development-friendly headers
        response.headers["X-Content-Type-Options"] = "nosniff"  # This one is always safe
        response.headers["X-Frame-Options"] = "SAMEORIGIN"  # Less restrictive for dev tools
        # Skip CSP and other strict headers that might interfere with development

    return response

templates = Jinja2Blocks(directory="templates")
jinja_partials.register_starlette_extensions(templates)

rendered_html_str:str = ""

# CSRF token helper for templates
def get_csrf_token(request: Request) -> str:
    """Generate and store CSRF token in session"""
    if not security_config.csrf_protection_enabled:
        return ""  # No CSRF in development

    csrf_token = CSRFProtection.generate_csrf_token()
    request.session["csrf_token"] = csrf_token
    return csrf_token

# Add CSRF validation helper
def validate_csrf(request: Request, csrf_token: Optional[str] = None) -> bool:
    """Validate CSRF token from form"""
    if not security_config.csrf_protection_enabled:
        return True  # Skip validation in development

    return CSRFProtection.validate_csrf_token(request, csrf_token or "")

# , user: User = Depends(current_user)
@app.get("/")
async def read_root(request: Request):
    return templates.TemplateResponse(
        "index.jinja2",
        {"request": request}
    )

@app.get("/logout")
async def logout():
    response = RedirectResponse(url="/", status_code=302)
    response.delete_cookie("auth")
    return response

@app.get("/userinfo")
async def userinfo(request: Request, user: User = Depends(current_user)):
    if user is None:
        return templates.TemplateResponse(
            "unauthanticated.jinja2",
            {"request": request}
        )
    else:
        # Get user settings from the database
        user_settings = await get_user_settings(user.email)
        return templates.TemplateResponse(
            "userinfo.jinja2",
            {"request": request, "user": user, "user_settings": user_settings}
        )

@app.post("/userinfo")
async def userinfo_save(request: Request, userinfoname: Annotated[str, Form()], userinfo: Annotated[str, Form()], user: User = Depends(current_user)):
    if user is None:
        return templates.TemplateResponse(
            "unauthanticated.jinja2",
            {"request": request}
        )

    try:
        # Update user settings in the database
        await update_user_settings(user.email, userinfoname, userinfo)
        print(f"User settings updated for {user.email}: name='{userinfoname}', info='{userinfo}'")

        async def _():
            yield SSE.redirect("/calculations")
        return DatastarResponse(_())

    except Exception as e:
        print(f"Error updating user settings for {user.email}: {e}")
        # Return error response
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Failed to save user information', "errortitle": 'Save failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

@app.get("/usercustomers")
async def usercustomers(request: Request, user: User = Depends(current_user)):
    if user is None:
        return templates.TemplateResponse(
            "unauthanticated.jinja2",
            {"request": request}
        )
    else:
        # Get user customers from the database
        customers = await get_user_customers(user.email)
        return templates.TemplateResponse(
            "usercustomers.jinja2",
            {"request": request, "user": user, "customers": customers, "add_new": False}
        )


@app.get("/add_customer")
async def add_customer(request: Request, user: User = Depends(current_user)):
    if user is None:
        return templates.TemplateResponse(
            "unauthanticated.jinja2",
            {"request": request}
        )
    else:
        # Get user customers and add an empty one for the new customer
        customers = await get_user_customers(user.email)
        templ = templates.get_template("usercustomers.jinja2")
        rendered_html_str = "".join(templ.blocks["content"](templ.new_context({
            "request": request,
            "user": user,
            "customers": customers,
            "add_new": True
        })))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, selector="#content-div", use_view_transition=False)
        return DatastarResponse(_())

@app.get("/userprojects")
async def userprojects(request: Request, user: User = Depends(current_user)):
    if user is None:
        return templates.TemplateResponse(
            "unauthanticated.jinja2",
            {"request": request}
        )
    else:
        return templates.TemplateResponse(
            "userprojects.jinja2",
            {"request": request, "user": user}
        )


@app.post("/customers")
async def handle_customers(request: Request, user: User = Depends(current_user)):
    if user is None:
        return templates.TemplateResponse(
            "unauthanticated.jinja2",
            {"request": request}
        )

    # Parse form data
    form_data = await request.form()
    action = str(form_data.get("action", ""))

    print(f"Debug - Received action: '{action}'")
    print(f"Debug - All form data: {dict(form_data)}")

    # If no action, assume save (fallback)
    if not action:
        action = "save"
        print("Debug - No action provided, defaulting to 'save'")

    try:
        if action == "delete":
            customer_id_str = str(form_data.get("customer_id", ""))
            customer_id = int(customer_id_str)
            await delete_customer(customer_id)

        elif action == "save":
            customer_id_str = str(form_data.get("customer_id", ""))
            name = str(form_data.get("name", "")).strip()
            info = str(form_data.get("info", "")).strip()

            print(f"Debug - Form data: {dict(form_data)}")
            print(f"Debug - customer_id: '{customer_id_str}', name: '{name}', info: '{info}'")

            if customer_id_str and customer_id_str != "":  # Update existing customer
                customer_id = int(customer_id_str)
                await update_customer(customer_id, name, info)
                print(f"Updated customer {customer_id}")
            else:  # Add new customer
                new_customer = await add_customer_to_user_new(user.email, name, info)
                print(f"Added new customer: {new_customer}")

        else:
            print(f"Debug - Unknown action: '{action}'")

        # Redirect back to customers page (all accordions closed)
        print("Debug - Operation completed, redirecting...")
        async def _():
            yield SSE.redirect("/usercustomers")
        return DatastarResponse(_())

    except Exception as e:
        print(f"Error handling customer operation: {e}")
        # Return error response
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({
            "request": request,
            "id": "errordiv",
            "signuperrormessage": str(e),
            "errortitle": "Customer Operation Failed"
        })))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())


@app.get("/login_form")
async def login(request: Request):
    # Security check: If there are query parameters (especially password), redirect to clean URL
    if request.query_params:
        # Log security incident - password exposed in URL
        SecurityLogger.log_security_event(
            "PASSWORD_IN_URL",
            {"query_params": str(request.query_params), "user_agent": request.headers.get("user-agent", "")},
            request.client.host if request.client else ""
        )
        # Redirect to clean login form URL
        return RedirectResponse(url="/login_form", status_code=302)

    csrf_token = get_csrf_token(request)
    context = {"request": request, "csrf_token": csrf_token, "environment": security_config.environment}
    return templates.TemplateResponse("login.jinja2", context)

@app.post("/login_validate")
@limiter.limit(security_config.get_rate_limit("auth"))
async def login_validate(request: Request, loginemail: Annotated[str, Form()], loginpass: Annotated[str, Form()], csrf_token: Annotated[str, Form()] = ""):
    # CSRF Protection
    if not validate_csrf(request, csrf_token):
        SecurityLogger.log_security_event("CSRF_VALIDATION_FAILED", {"endpoint": "/login_validate"}, request.client.host if request.client else "")
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Security validation failed', "errortitle": 'Login Failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

    # Input sanitization
    loginemail = InputSanitizer.sanitize_email(loginemail)

    # Validation
    email_valid, email_error = SecurityValidator.validate_email_security(loginemail)
    if not email_valid:
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": email_error, "errortitle": 'Login Failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

    print('username:' + loginemail)
    if is_development():
        print('password:' + loginpass)
    else:
        print('password: ***')

    # Use local URL for internal authentication to avoid external network issues
    import os
    port = os.getenv("PORT", "8000")
    if is_development():
        LOGIN_URL = f"http://localhost:{port}/auth/jwt/login"
    else:
        LOGIN_URL = f"http://127.0.0.1:{port}/auth/jwt/login"
    print('LOGIN_URL:' + LOGIN_URL)

    login_data = {
    "grant_type": "password",
    "username": loginemail,
    "password": loginpass,
    "scope": "",
    "client_id": "",
    "client_secret": ""
    }

    async with httpx.AsyncClient() as client:
        response = await client.post(
            LOGIN_URL,
            data=login_data,
            headers={
                "accept": "application/json",
                "Content-Type": "application/x-www-form-urlencoded",
            }
        )

    if response.status_code == 200 or response.status_code == 204:
        # Log successful authentication
        SecurityLogger.log_auth_attempt(loginemail, True, request.client.host if request.client else "", request.headers.get("user-agent", ""))

        print(str(response.cookies.get('auth')))
        cookie_value = response.cookies.get('auth', '')
        if cookie_value is None:
            raise ValueError("JWT auth cookie was not found in the login response")
        client.cookies.set("auth", cookie_value)

        # Debug logging for Safari cookie issues
        if is_development():
            print(f"🍪 Setting auth cookie:")
            print(f"   Cookie value length: {len(cookie_value) if cookie_value else 0}")
            print(f"   User-Agent: {request.headers.get('user-agent', '')}")
            print(f"   Is Safari: {'Safari' in request.headers.get('user-agent', '')}")
            print(f"   Is Mobile: {'Mobile' in request.headers.get('user-agent', '')}")
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["success_and_redirect"](templ.new_context({"request": request, "title":"Login Successful!", "text":"Redirecting"})))
        async def _():
            try:
                yield SSE.patch_signals({"signalShowContentTransition": True})
                yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
                yield SSE.execute_script(
                    script='(function(){if(document.readyState==="loading"){window.addEventListener("DOMContentLoaded",function(){setTimeout(function(){location.href="/calculations";},2000);});}else{setTimeout(function(){location.href="/calculations";},2000);}})();',
                    auto_remove=True
                )
            except Exception as e:
                print(f"Exception in SSE generator: {e}")
        streaming_response = DatastarResponse(_())

        # Use the same cookie configuration as FastAPI-Users
        from app.users import cookie_transport
        cookie_config = cookie_transport

        # Build cookie attributes based on environment
        cookie_attributes = [
            f"auth={cookie_value}",
            "HttpOnly",
            f"Max-Age={cookie_config.cookie_max_age}",
            f"Path={cookie_config.cookie_path or '/'}",
        ]

        # Add Secure flag only in production (HTTPS)
        if cookie_config.cookie_secure:
            cookie_attributes.append("Secure")

        # Add SameSite attribute based on environment
        if cookie_config.cookie_samesite:
            cookie_attributes.append(f"SameSite={cookie_config.cookie_samesite}")

        cookie_header = "; ".join(cookie_attributes)
        streaming_response.headers['Set-Cookie'] = cookie_header

        # Debug logging for cookie configuration
        if is_development():
            print(f"🍪 Cookie header set: {cookie_header}")
            print(f"   Environment: {security_config.environment}")
            print(f"   Cookie secure: {cookie_config.cookie_secure}")
            print(f"   Cookie samesite: {cookie_config.cookie_samesite}")

        return streaming_response

    elif response.status_code == 400:
        # Log failed authentication
        SecurityLogger.log_auth_attempt(loginemail, False, request.client.host if request.client else "", request.headers.get("user-agent", ""))

        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Invalid credentials', "errortitle": 'Login failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())
    else:
        # Log failed authentication with unexpected status
        SecurityLogger.log_auth_attempt(loginemail, False, request.client.host if request.client else "", request.headers.get("user-agent", ""))

        templ = templates.get_template("snippets.jinja2")
        error_message = 'Login failed' if security_config.is_production else f"Login failed with status code: {response.status_code}"
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": error_message, "errortitle": 'Login failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

@app.get("/forgotpasswordemailform")
async def forgotpasswordemailform(request: Request):
    return templates.TemplateResponse("forgotpasswordemailform.jinja2", {"request": request})

@app.get("/verify_email")
@limiter.limit(security_config.get_rate_limit("email"))
async def verify_email_submit(request: Request, token: str):
    try:
        # Make POST request to FastAPI-Users verify endpoint
        url = f"{BASE_URL}/auth/verify"
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        data = {"token": token}

        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, json=data)

        print(f"Verification response status: {response.status_code}")
        print(f"Verification response: {response.text}")

        if response.status_code == 200:
            print("Verification successful")
            # Return regular template response with JavaScript redirect
            return templates.TemplateResponse("email-verified.jinja2", {
                "request": request
            })
        else:
            print("Verification failed")
            error_detail = response.json().get("detail", "Verification failed")
            templ = templates.get_template("snippets.jinja2")
            rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": error_detail, "errortitle": 'Email Verification Failed'})))
            async def _():
                yield SSE.patch_signals({"signalShowContentTransition": False})
                yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
            return DatastarResponse(_())

    except Exception as e:
        print(f"Error during email verification: {e}")
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'An error occurred during verification', "errortitle": 'Email Verification Failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

@app.post("/forgotpassword_sendemail")
async def forgotpassword_sendemail(request: Request, forgotpasswordemail: Annotated[str, Form()], user_manager=Depends(get_user_manager)):
    try:
        user = await user_manager.get_by_email(forgotpasswordemail)
        print(f"user.id: {user.id}")
        if user.id:
            await user_manager.forgot_password(user)
        else:
            time.sleep(2)
    except Exception as e:
        print(f"Error during password reset: {e}")

    templ = templates.get_template("forgotpasswordemailsent.jinja2")
    rendered_html_str = "".join(templ.blocks["content"](templ.new_context({"request": request})))
    async def _():
        yield SSE.patch_signals({"signalShowContentTransition": True})
        yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
    return DatastarResponse(_())

@app.get("/reset-password")  # The link on the email brings here
async def forgotpasswordchange(request: Request, token: str):
    return templates.TemplateResponse("forgotpasswordchange.jinja2", {"request": request, "token": token})

@app.post("/forgotpassword_reset") # forgotpasswordchange.jinja2 Submit with 2 password fields brings here
async def forgotpassword_reset(request: Request, token: str, forgotpass: Annotated[str, Form()], forgotpassrepeat: Annotated[str, Form()]):
    print('token:' + token)
    print('password:' + forgotpass)
    print('passwordrepeat:' + forgotpassrepeat)

    if len(token) < 20 or not token:
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Problem with token', "errortitle": 'Password Reset failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

    if not forgotpass == forgotpassrepeat:
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Password Repeat missmatch', "errortitle": 'Password Reset failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

    url = "http://localhost:8000/auth/reset-password"
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    data = {
        "token": token,
        "password": forgotpass
    }
    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=headers, json=data)
    print("Status Code:", response.status_code)
    print("Response:", response.json())
    if response.status_code == 200:
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["resetpasswordsuccess"](templ.new_context({"request": request})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": True})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())
    elif response.status_code == 400:
        code_value = response.json()["detail"]
        if code_value == 'RESET_PASSWORD_BAD_TOKEN':
            templ = templates.get_template("snippets.jinja2")
            rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Reset Password Bad Token', "errortitle": 'Password Reset failed'})))
            async def _():
                yield SSE.patch_signals({"signalShowContentTransition": False})
                yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
            return DatastarResponse(_())
    elif response.status_code == 422:
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Validation Error', "errortitle": 'Password Reset failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())
    print("Unknown return Code!!")
    return "Unknown return Code!!"


@app.get("/signup_form")
def signup_form(request: Request):
    csrf_token = get_csrf_token(request)
    context = {"request": request, "csrf_token": csrf_token, "environment": security_config.environment}

    if request.headers.get('datastar-request') == 'true':
        templ = templates.get_template("signup.jinja2")
        rendered_html_str = "".join(templ.blocks["content"](templ.new_context(context)))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": True})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())
    else:
        return templates.TemplateResponse("signup.jinja2", context)

@app.post("/signup_validate")
@limiter.limit(security_config.get_rate_limit("auth"))
async def signup_validate(request: Request, signupemail: Annotated[str, Form()], signuppass: Annotated[str, Form()], signuppassrepeat: Annotated[str, Form()], terms_agreed: Annotated[str, Form()] = "", csrf_token: Annotated[str, Form()] = "", user_manager=Depends(get_user_manager)):
    # CSRF Protection
    if not validate_csrf(request, csrf_token):
        SecurityLogger.log_security_event("CSRF_VALIDATION_FAILED", {"endpoint": "/signup_validate"}, request.client.host if request.client else "")
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Security validation failed', "errortitle": 'Sign Up Failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

    # Input sanitization
    signupemail = InputSanitizer.sanitize_email(signupemail)

    # Enhanced validation
    email_valid, email_error = SecurityValidator.validate_email_security(signupemail)
    if not email_valid:
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": email_error, "errortitle": 'Sign Up Failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

    password_valid, password_error = SecurityValidator.validate_password_strength(signuppass)
    if not password_valid:
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": password_error, "errortitle": 'Sign Up Failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

    print('email:' + signupemail)
    if is_development():
        print('password:' + signuppass)
        print('passwordrepeat:' + signuppassrepeat)
    else:
        print('password: ***')
        print('passwordrepeat: ***')

    # Check terms agreement
    if terms_agreed != "on":
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'You must agree to the Privacy Policy and Terms and Conditions', "errortitle": 'Sign Up Failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

    if not signuppass == signuppassrepeat:
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Password Repeat missmatch', "errortitle": 'Sign Up failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())

    url = f"{BASE_URL}/auth/register"
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json"
    }
    data = {
        "email": signupemail,
        "password": signuppass,
        "is_superuser": False,
        "is_verified": False
    }
    async with httpx.AsyncClient() as client:
        response = await client.post(url, headers=headers, json=data)
    print("Status Code:", response.status_code)
    print("Response:", response.json())
    if response.status_code == 201:
        # Create user settings entry after successful signup
        # try:
        #     await create_user_settings_entry(email=signupemail, name="", info="")
        #     print(f"User settings created for email: {signupemail}")
        # except Exception as e:
        #     print(f"Error creating user settings for {signupemail}: {e}")
            # Continue with signup success even if user settings creation fails

        # After successful signup, trigger email verification
        try:
            user = await user_manager.get_by_email(signupemail)
            if user:
                print(f"Triggering request_verify for user: {signupemail}")
                print(f"User object: id={user.id}, email={user.email}, is_active={user.is_active}, is_verified={user.is_verified}")

                # Temporarily activate user to send verification email
                await user_manager.user_db.update(user, {"is_active": True})
                print(f"Temporarily activated user for verification email")

                # Send verification email
                await user_manager.request_verify(user)
                print(f"request_verify completed successfully")

                # Deactivate user again until they verify
                await user_manager.user_db.update(user, {"is_active": False})
                print(f"User deactivated again until email verification")
            else:
                print(f"User not found for email: {signupemail}")
        except Exception as e:
            print(f"Error sending verification email for {signupemail}: {e}")
            print(f"Error type: {type(e)}")
            import traceback
            traceback.print_exc()

        templ = templates.get_template("signupemailsent.jinja2")
        rendered_html_str = "".join(templ.blocks["content"](templ.new_context({"request": request})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": True})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())
    elif response.status_code == 400:
        code_value = response.json()["detail"]
        if code_value == 'REGISTER_USER_ALREADY_EXISTS':
            templ = templates.get_template("snippets.jinja2")
            rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Email address already in use', "errortitle": 'Sign Up failed'})))
            async def _():
                yield SSE.patch_signals({"signalShowContentTransition": False})
                yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
            return DatastarResponse(_())
        elif code_value == 'REGISTER_INVALID_PASSWORD':
            templ = templates.get_template("snippets.jinja2")
            rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Invalid Password', "errortitle": 'Sign Up failed'})))
            async def _():
                yield SSE.patch_signals({"signalShowContentTransition": False})
                yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
            return DatastarResponse(_())
    elif response.status_code == 422:
        templ = templates.get_template("snippets.jinja2")
        rendered_html_str = "".join(templ.blocks["errorblock"](templ.new_context({"request": request, "id":"errordiv", "signuperrormessage": 'Validation Error', "errortitle": 'Sign Up failed'})))
        async def _():
            yield SSE.patch_signals({"signalShowContentTransition": False})
            yield SSE.patch_elements(fragments=rendered_html_str, use_view_transition=True)
        return DatastarResponse(_())
    print("Unknown return Code!!")
    return "Unknown return Code!!"

# USERS
app.include_router(
    fastapi_users.get_auth_router(auth_backend, requires_verification=True), prefix="/auth/jwt", tags=["auth"]
)
app.include_router(
    fastapi_users.get_register_router(UserRead, UserCreate),
    prefix="/auth",
    tags=["auth"],
)
app.include_router(
    fastapi_users.get_reset_password_router(),
    prefix="/auth",
    tags=["auth"],
)
app.include_router(
    fastapi_users.get_verify_router(UserRead),
    prefix="/auth",
    tags=["auth"],
)
app.include_router(
    fastapi_users.get_users_router(UserRead, UserUpdate),
    prefix="/users",
    tags=["users"],
)

# /{subpage}
@app.get("/calculations")
async def authenticated_route(request: Request, user: User = Depends(current_user)):
    # Debug logging for Safari authentication issues
    user_agent = request.headers.get("user-agent", "")
    auth_cookie = request.cookies.get("auth")

    if is_development():
        print(f"🔍 /calculations route accessed:")
        print(f"   User-Agent: {user_agent}")
        print(f"   Auth cookie present: {'Yes' if auth_cookie else 'No'}")
        print(f"   Auth cookie value: {auth_cookie[:20] + '...' if auth_cookie else 'None'}")
        print(f"   User object: {user}")
        print(f"   All cookies: {dict(request.cookies)}")

    if user is None:
        # Log authentication failure for debugging
        if is_development():
            print(f"❌ Authentication failed for /calculations")
            # print(f"   Headers: {dict(request.headers)}")

        return templates.TemplateResponse(
            "unauthanticated.jinja2",
            {"request": request}
        )
    else:
        if is_development():
            print(f"✅ Authentication successful for user: {user.email}")

        customers = await get_user_customers(user.email)
        customer_names = [customer["name"] for customer in customers]
        return templates.TemplateResponse(
            "calc01.jinja2",
            {"request": request, "user": user, "customer_names": customer_names}
        )




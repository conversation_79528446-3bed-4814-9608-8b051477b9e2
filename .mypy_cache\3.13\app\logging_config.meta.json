{"data_mtime": 1751458416, "dep_lines": [7, 13, 6, 8, 9, 10, 11, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["logging.handlers", "app.config", "logging", "os", "datetime", "typing", "json", "builtins", "_frozen_importlib", "_typeshed", "abc", "json.encoder"], "hash": "cb7bd8d0b4a5a63eb8add2f083be636daff1e506", "id": "app.logging_config", "ignore_all": false, "interface_hash": "401095dc76cf050c7b83498c42837878e60f876a", "mtime": 1752573338, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\logging_config.py", "plugin_data": null, "size": 6042, "suppressed": [], "version_id": "1.15.0"}
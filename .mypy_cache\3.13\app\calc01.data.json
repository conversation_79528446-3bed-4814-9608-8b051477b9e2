{".class": "MypyFile", "_fullname": "app.calc01", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIRouter": {".class": "SymbolTableNode", "cross_ref": "fastapi.routing.APIRouter", "kind": "Gdef"}, "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing.Annotated", "kind": "Gdef"}, "DatastarResponse": {".class": "SymbolTableNode", "cross_ref": "datastar_py.starlette.DatastarResponse", "kind": "Gdef"}, "Depends": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Depends", "kind": "Gdef"}, "FPDF": {".class": "SymbolTableNode", "cross_ref": "fpdf.fpdf.FPDF", "kind": "Gdef"}, "Form": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Form", "kind": "Gdef"}, "Jinja2Blocks": {".class": "SymbolTableNode", "cross_ref": "jinja2_fragments.fastapi.Jinja2Blocks", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.Request", "kind": "Gdef"}, "SSE": {".class": "SymbolTableNode", "cross_ref": "datastar_py.sse.ServerSentEventGenerator", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "cross_ref": "app.db.User", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.calc01.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.calc01.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.calc01.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.calc01.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.calc01.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.calc01.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "calc01_submit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["request", "select_customer", "PE", "sp_flow", "sp_BOD5", "sp_COD", "sp_SS", "sp_TN", "sp_P", "safety_factor", "svi", "primary", "sludge_storage_duration", "wwtp_type", "separate_deintrification_chamber", "qsv", "primary_duration", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "app.calc01.calc01_submit", "name": "calc01_submit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["request", "select_customer", "PE", "sp_flow", "sp_BOD5", "sp_COD", "sp_SS", "sp_TN", "sp_P", "safety_factor", "svi", "primary", "sludge_storage_duration", "wwtp_type", "separate_deintrification_chamber", "qsv", "primary_duration", "user"], "arg_types": ["starlette.requests.Request", "builtins.str", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.bool", "builtins.int", "builtins.str", "builtins.bool", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calc01_submit", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.calc01.calc01_submit", "name": "calc01_submit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1], "arg_names": ["request", "select_customer", "PE", "sp_flow", "sp_BOD5", "sp_COD", "sp_SS", "sp_TN", "sp_P", "safety_factor", "svi", "primary", "sludge_storage_duration", "wwtp_type", "separate_deintrification_chamber", "qsv", "primary_duration", "user"], "arg_types": ["starlette.requests.Request", "builtins.str", "builtins.int", "builtins.int", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.int", "builtins.bool", "builtins.int", "builtins.str", "builtins.bool", "builtins.float", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, "app.db.User"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "calc01_submit", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "current_user": {".class": "SymbolTableNode", "cross_ref": "app.users.current_user", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "get_user_customers": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.get_user_customers", "kind": "Gdef"}, "get_user_settings": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.get_user_settings", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "register_fonts_on_pdf": {".class": "SymbolTableNode", "cross_ref": "static.pdf.fontloader.register_fonts_on_pdf", "kind": "Gdef"}, "router": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.calc01.router", "name": "router", "type": "fastapi.routing.APIRouter"}}, "templates": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.calc01.templates", "name": "templates", "type": "jinja2_fragments.fastapi.Jinja2Blocks"}}}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\calc01.py"}
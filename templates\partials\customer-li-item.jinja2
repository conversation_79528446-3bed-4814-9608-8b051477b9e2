<div>
  <style>
    me {
        width: 100%;
        height: auto;
    }
  </style>

  <form data-signals="{_{{ namealwayschange }}_customer_submit_button_disable:false, _{{ namealwayschange }}_form_invalid:false}" data-on-submit="$_{{ namealwayschange }}_customer_submit_button_disable = true;@post('/customers', {contentType: 'form'})">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
    <style>
      me {
          animation: cardAppear 0.4s ease-out;
      }

      @keyframes cardAppear {
          from {
              opacity: 0;
              transform: translateY(-40px);
          }
          to {
              opacity: 1;
              transform: translateY(0);
          }
      }
    </style>
    <input type="hidden" name="action" value="save">
    <input type="hidden" name="customer_id" value="{{ customer_id }}">


    {# INPUT #}
    <div data-on-input="let name = document.getElementById('{{ namealwayschange }}name'); $_{{ namealwayschange }}_form_invalid = !(name?.checkValidity())">
      {{ render_partial('partials/forms-input.jinja2',
         namealwayschange=namealwayschange + 'name',
         name='name',
         label='Name',
         type='text',
         pattern=".{4,}",
         errormessage="Name must be at least 4 characters",
         value=customer_name,
         id=namealwayschange ~ 'name') }}
    </div>


    {# TEXTAREA #}
    <div class="mycooltextarea">
      <label for="{{ namealwayschange }}info">Info</label>
      <textarea name="info" id="{{ namealwayschange }}info" onkeydown="if(this.value.split('\n').length > 5 && event.key === 'Enter') event.preventDefault();" onpaste="setTimeout(() => { const lines = this.value.split('\n'); if(lines.length > 6) this.value = lines.slice(0, 6).join('\n'); }, 0);">{{ customer_info | default('') }}</textarea>
    </div>

    {# SUBMIT BUTTON #}
    <button type="submit" data-attr-disabled="$_{{ namealwayschange }}_form_invalid || $_{{ namealwayschange }}_customer_submit_button_disable">
      <style>
          me {
            height: 40px;
            margin-top: 14px;
            margin-bottom: 10px;
            width: 100%;
            display: block;
            background-color: transparent;
            color: var(--color-text-dark);
            border: 1px solid var(--color-text-dark);
            cursor: pointer;
            border-width: 1px;
            border-radius: 6px;
            font-family: 'Noto Sans', sans-serif;
            font-weight: 500;
            font-size: 18px;
            font-stretch: semi-condensed;
            text-align: center;
            transition: background-color 0.3s ease, color 0.3s ease;
          }

          me:hover {
            background-color: var(--color-background-dark);
            color: var(--color-text-bright);
          }

          me:disabled {
            background-color: var(--color-disabled-background);
            color: var(--color-text-black);
            opacity: 0.6;
            cursor: not-allowed;
            padding: 0px 0px;
          }

          me .button-spinner {
            display: none;
            width: 30px;
            height: 30px;
          }
      </style>
      <span class="button-text" data-attr-style="$_{{ namealwayschange }}_customer_submit_button_disable ? 'display: none' : 'display: inline'">Save</span>
      <img class="button-spinner" data-attr-style="$_{{ namealwayschange }}_customer_submit_button_disable ? 'display: inline; margin-top: 4px; margin-bottom: 0px;' : 'display: none'"
            src="/static/images/tube-spinner.svg"
            alt="spinning..." />
    </button>

  </form>

  {# Delete Button #}
  {% if customer_id %}
  <form data-on-submit="@post('/customers', {contentType: 'form'})">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
    <input type="hidden" name="action" value="delete">
    <input type="hidden" name="customer_id" value="{{ customer_id }}">
    <button type="submit" onclick="event.preventDefault(); Swal.fire({
    title: 'Are you sure?',
    text: 'You want to delete this customer?',
    icon: 'warning',
    showCancelButton: true,
    confirmButtonText: 'Delete',
    backdrop: false,
    customClass: {
      popup: 'custom-swal-popup',
      title: 'custom-swal-title',
      htmlContainer: 'custom-swal-text',
      confirmButton: 'custom-swal-confirm',
      cancelButton: 'custom-swal-cancel'
    },
    showClass: {
      popup: 'swal2-show-custom'
    },
    hideClass: {
      popup: 'swal2-hide-custom'
    }
  }).then((result) => {if (result.isConfirmed) {this.closest('form').dispatchEvent(new Event('submit', {bubbles: true}));}})">
      <style>
          me {
              height: 40px;
              margin-top: 14px;
              margin-bottom: 10px;
              width: 100%;
              display: block;
              background-color: transparent;
              color: var(--color-selected-red);
              border: 1px solid var(--color-error-title);
              cursor: pointer;
              border-width: 1px;
              border-radius: 6px;
              font-family: 'Noto Sans', sans-serif;
              font-weight: 500;
              font-size: 18px;
              font-stretch: semi-condensed;
              text-align: center;
              transition: background-color 0.3s ease, color 0.3s ease;
          }

          me:hover {
              background-color: var(--color-selected-red);
              color: var(--color-text-bright);
          }
      </style>
      Delete
    </button>

    <style>
      .custom-swal-popup {
        font-family: 'Noto Sans', sans-serif;
        background-color: var(--color-background-bright);
        border: 2px solid var(--color-input-lines);
        border-radius: 16px;
      }

      .custom-swal-title {
        font-family: 'Noto Serif', serif;
        font-size: 24px;
        font-weight: 600;
        color: var(--color-text-black);
      }

      .custom-swal-text {
        font-family: 'Noto Sans', sans-serif;
        font-size: 18px;
        font-weight: 400;
        color: var(--color-text-black);
      }

      .custom-swal-confirm {
        font-family: 'Noto Sans', sans-serif;
        font-size: 16px;
        font-weight: 500;
        border-radius: 6px;
        padding: 12px 24px;
        background-color: var(--color-error-title);
        color: var(--color-text-bright);
        border: 1px solid var(--color-error-title);
      }

      .custom-swal-confirm:hover {
        background-color: var(--color-selected-red);
      }

      .custom-swal-cancel {
        font-family: 'Noto Sans', sans-serif;
        font-size: 16px;
        font-weight: 500;
        border-radius: 6px;
        padding: 12px 24px;
        background-color: var(--color-background-dark);
        color: var(--color-text-bright);
        border: 1px solid var(--color-text-dark);
      }

      .custom-swal-cancel:hover {
        background-color: var(--color-background-middle);
      }

      .swal2-icon.swal2-warning {
        border-color: var(--color-selected-red);
        color: var(--color-selected-red);
      }

      /* Custom subtle popup animation */
      .swal2-show-custom {
        animation: swal2-show-custom 400ms cubic-bezier(.63,.7,.16,1.28);
      }

      .swal2-hide-custom {
        animation: swal2-hide-custom 300ms ease-out;
      }

      @keyframes swal2-show-custom {
        0% {
          transform: scale(0.7);
          opacity: 0;
        }
        100% {
          transform: scale(1);
          opacity: 1;
        }
      }

      @keyframes swal2-hide-custom {
        0% {
          transform: scale(1);
          opacity: 1;
        }
        100% {
          transform: scale(0.8);
          opacity: 0;
        }
      }


    </style>
  </form>
  {% endif %}

</div>

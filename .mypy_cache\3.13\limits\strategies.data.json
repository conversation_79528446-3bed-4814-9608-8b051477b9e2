{".class": "MypyFile", "_fullname": "limits.strategies", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ABCMeta": {".class": "SymbolTableNode", "cross_ref": "abc.ABCMeta", "kind": "Gdef"}, "FixedWindowRateLimiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["limits.strategies.RateLimiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "limits.strategies.FixedWindowRateLimiter", "name": "FixedWindowRateLimiter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "limits.strategies.FixedWindowRateLimiter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "limits.strategies", "mro": ["limits.strategies.FixedWindowRateLimiter", "limits.strategies.RateLimiter", "builtins.object"], "names": {".class": "SymbolTable", "get_window_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.FixedWindowRateLimiter.get_window_stats", "name": "get_window_stats", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "arg_types": ["limits.strategies.FixedWindowRateLimiter", "limits.limits.RateLimitItem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_stats of FixedWindowRateLimiter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "limits.util.WindowStats"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.FixedWindowRateLimiter.hit", "name": "hit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "arg_types": ["limits.strategies.FixedWindowRateLimiter", "limits.limits.RateLimitItem", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hit of FixedWindowRateLimiter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.FixedWindowRateLimiter.test", "name": "test", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "arg_types": ["limits.strategies.FixedWindowRateLimiter", "limits.limits.RateLimitItem", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test of FixedWindowRateLimiter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "limits.strategies.FixedWindowRateLimiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "limits.strategies.FixedWindowRateLimiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "KnownStrategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "limits.strategies.KnownStrategy", "line": 308, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeType", "item": "limits.strategies.SlidingWindowCounterRateLimiter"}, {".class": "TypeType", "item": "limits.strategies.FixedWindowRateLimiter"}, {".class": "TypeType", "item": "limits.strategies.MovingWindowRateLimiter"}], "uses_pep604_syntax": true}}}, "MovingWindowRateLimiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["limits.strategies.RateLimiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "limits.strategies.MovingWindowRateLimiter", "name": "MovingWindowRateLimiter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "limits.strategies.MovingWindowRateLimiter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "limits.strategies", "mro": ["limits.strategies.MovingWindowRateLimiter", "limits.strategies.RateLimiter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "storage"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.MovingWindowRateLimiter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "storage"], "arg_types": ["limits.strategies.MovingWindowRateLimiter", {".class": "TypeAliasType", "args": [], "type_ref": "limits.storage.StorageTypes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MovingWindowRateLimiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_window_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.MovingWindowRateLimiter.get_window_stats", "name": "get_window_stats", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "arg_types": ["limits.strategies.MovingWindowRateLimiter", "limits.limits.RateLimitItem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_stats of MovingWindowRateLimiter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "limits.util.WindowStats"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.MovingWindowRateLimiter.hit", "name": "hit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "arg_types": ["limits.strategies.MovingWindowRateLimiter", "limits.limits.RateLimitItem", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hit of MovingWindowRateLimiter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.MovingWindowRateLimiter.test", "name": "test", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "arg_types": ["limits.strategies.MovingWindowRateLimiter", "limits.limits.RateLimitItem", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test of MovingWindowRateLimiter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "limits.strategies.MovingWindowRateLimiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "limits.strategies.MovingWindowRateLimiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MovingWindowSupport": {".class": "SymbolTableNode", "cross_ref": "limits.storage.base.MovingWindowSupport", "kind": "Gdef"}, "RateLimitItem": {".class": "SymbolTableNode", "cross_ref": "limits.limits.RateLimitItem", "kind": "Gdef"}, "RateLimiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["get_window_stats", 1], ["hit", 1], ["test", 1]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": "abc.ABCMeta", "defn": {".class": "ClassDef", "fullname": "limits.strategies.RateLimiter", "name": "RateLimiter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "limits.strategies.RateLimiter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "limits.strategies", "mro": ["limits.strategies.RateLimiter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "storage"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.RateLimiter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "storage"], "arg_types": ["limits.strategies.RateLimiter", {".class": "TypeAliasType", "args": [], "type_ref": "limits.storage.StorageTypes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RateLimiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.RateLimiter.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "arg_types": ["limits.strategies.RateLimiter", "limits.limits.RateLimitItem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of RateLimiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_window_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "limits.strategies.RateLimiter.get_window_stats", "name": "get_window_stats", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "arg_types": ["limits.strategies.RateLimiter", "limits.limits.RateLimitItem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_stats of RateLimiter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "limits.util.WindowStats"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "limits.strategies.RateLimiter.get_window_stats", "name": "get_window_stats", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "arg_types": ["limits.strategies.RateLimiter", "limits.limits.RateLimitItem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_stats of RateLimiter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "limits.util.WindowStats"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "hit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "limits.strategies.RateLimiter.hit", "name": "hit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "arg_types": ["limits.strategies.RateLimiter", "limits.limits.RateLimitItem", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hit of RateLimiter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "limits.strategies.RateLimiter.hit", "name": "hit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "arg_types": ["limits.strategies.RateLimiter", "limits.limits.RateLimitItem", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hit of RateLimiter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "storage": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "limits.strategies.RateLimiter.storage", "name": "storage", "type": "limits.storage.base.Storage"}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "limits.strategies.RateLimiter.test", "name": "test", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "arg_types": ["limits.strategies.RateLimiter", "limits.limits.RateLimitItem", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test of RateLimiter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "limits.strategies.RateLimiter.test", "name": "test", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "arg_types": ["limits.strategies.RateLimiter", "limits.limits.RateLimitItem", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test of RateLimiter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "limits.strategies.RateLimiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "limits.strategies.RateLimiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "STRATEGIES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "limits.strategies.STRATEGIES", "name": "STRATEGIES", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "limits.strategies.KnownStrategy"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "SlidingWindowCounterRateLimiter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["limits.strategies.RateLimiter"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "limits.strategies.SlidingWindowCounterRateLimiter", "name": "SlidingWindowCounterRateLimiter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "limits.strategies.SlidingWindowCounterRateLimiter", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "limits.strategies", "mro": ["limits.strategies.SlidingWindowCounterRateLimiter", "limits.strategies.RateLimiter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "storage"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.SlidingWindowCounterRateLimiter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "storage"], "arg_types": ["limits.strategies.SlidingWindowCounterRateLimiter", {".class": "TypeAliasType", "args": [], "type_ref": "limits.storage.StorageTypes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SlidingWindowCounterRateLimiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_weighted_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "item", "previous_count", "previous_expires_in", "current_count"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.SlidingWindowCounterRateLimiter._weighted_count", "name": "_weighted_count", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "item", "previous_count", "previous_expires_in", "current_count"], "arg_types": ["limits.strategies.SlidingWindowCounterRateLimiter", "limits.limits.RateLimitItem", "builtins.int", "builtins.float", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_weighted_count of SlidingWindowCounterRateLimiter", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.SlidingWindowCounterRateLimiter.clear", "name": "clear", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "arg_types": ["limits.strategies.SlidingWindowCounterRateLimiter", "limits.limits.RateLimitItem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear of SlidingWindowCounterRateLimiter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_window_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.SlidingWindowCounterRateLimiter.get_window_stats", "name": "get_window_stats", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2], "arg_names": ["self", "item", "identifiers"], "arg_types": ["limits.strategies.SlidingWindowCounterRateLimiter", "limits.limits.RateLimitItem", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_window_stats of SlidingWindowCounterRateLimiter", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "limits.util.WindowStats"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "hit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.SlidingWindowCounterRateLimiter.hit", "name": "hit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "arg_types": ["limits.strategies.SlidingWindowCounterRateLimiter", "limits.limits.RateLimitItem", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hit of SlidingWindowCounterRateLimiter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "limits.strategies.SlidingWindowCounterRateLimiter.test", "name": "test", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5], "arg_names": ["self", "item", "identifiers", "cost"], "arg_types": ["limits.strategies.SlidingWindowCounterRateLimiter", "limits.limits.RateLimitItem", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test of SlidingWindowCounterRateLimiter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "limits.strategies.SlidingWindowCounterRateLimiter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "limits.strategies.SlidingWindowCounterRateLimiter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SlidingWindowCounterSupport": {".class": "SymbolTableNode", "cross_ref": "limits.storage.base.SlidingWindowCounterSupport", "kind": "Gdef"}, "Storage": {".class": "SymbolTableNode", "cross_ref": "limits.storage.base.Storage", "kind": "Gdef"}, "StorageTypes": {".class": "SymbolTableNode", "cross_ref": "limits.storage.StorageTypes", "kind": "Gdef"}, "WindowStats": {".class": "SymbolTableNode", "cross_ref": "limits.util.WindowStats", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.strategies.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.strategies.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.strategies.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.strategies.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.strategies.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "limits.strategies.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "floor": {".class": "SymbolTableNode", "cross_ref": "math.floor", "kind": "Gdef"}, "inf": {".class": "SymbolTableNode", "cross_ref": "math.inf", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "versionadded": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "limits.strategies.versionadded", "name": "versionadded", "type": {".class": "AnyType", "missing_import_name": "limits.strategies.versionadded", "source_any": null, "type_of_any": 3}}}}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\limits\\strategies.py"}
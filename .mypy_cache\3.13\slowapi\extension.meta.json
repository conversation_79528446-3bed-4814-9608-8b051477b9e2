{"data_mtime": 1752571437, "dep_lines": [12, 27, 28, 29, 30, 31, 32, 33, 36, 37, 4, 5, 6, 7, 8, 9, 10, 11, 14, 26, 34, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["email.utils", "limits.errors", "limits.storage", "limits.strategies", "starlette.config", "starlette.datastructures", "starlette.requests", "starlette.responses", "slowapi.errors", "slowapi.wrappers", "asyncio", "functools", "inspect", "itertools", "logging", "os", "time", "datetime", "typing", "limits", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "limits.aio", "limits.aio.storage", "limits.aio.storage.base", "limits.limits", "limits.storage.base", "limits.storage.memory", "limits.storage.registry", "limits.util", "pathlib", "starlette", "starlette.background", "starlette.exceptions", "types"], "hash": "9c7ed90436f6fa6b22bf8035b91e2463b1298c18", "id": "slowapi.extension", "ignore_all": true, "interface_hash": "c0cdc1e8f32580f540194caea5f10258d03ccaa8", "mtime": 1752570205, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Local\\Projects\\BioCleaning\\.venv\\Lib\\site-packages\\slowapi\\extension.py", "plugin_data": null, "size": 35120, "suppressed": [], "version_id": "1.15.0"}